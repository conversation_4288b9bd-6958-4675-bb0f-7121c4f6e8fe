#!/usr/bin/env python3
"""
词级别时间戳功能测试脚本
测试音频预处理到转录的完整流程，验证词级别时间戳是否正常工作
"""

import os
import sys
import json
import time
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from audio_processor.transcribing.transcriber import WhisperTranscriber
from audio_processor.preprocessing.processor import AudioPreprocessor


def test_word_timestamps():
    """测试词级别时间戳功能"""
    print("=" * 60)
    print("词级别时间戳功能测试")
    print("=" * 60)
    
    # 测试音频文件路径
    audio_file = "data/samples/过量（二）.mp3"
    
    if not os.path.exists(audio_file):
        print(f"❌ 测试音频文件不存在: {audio_file}")
        return False
    
    print(f"📁 测试音频文件: {audio_file}")
    
    try:
        # 1. 测试不启用预处理的词级别时间戳
        print("\n" + "=" * 40)
        print("测试1: 不启用预处理的词级别时间戳")
        print("=" * 40)
        
        transcriber_no_prep = WhisperTranscriber(enable_preprocessing=False)
        
        # 明确指定词级别时间戳参数
        result_no_prep = transcriber_no_prep.transcribe(
            audio_file,
            return_timestamps=True,
            timestamp_granularity="word"
        )
        
        print(f"✅ 转录完成 (无预处理)")
        print(f"📝 转录文本: {result_no_prep['text'][:100]}...")
        print(f"⏱️  处理时间: {result_no_prep['processing_time']:.2f}秒")
        
        # 检查是否有词级别时间戳
        if 'chunks' in result_no_prep:
            chunks = result_no_prep['chunks']
            print(f"📊 检测到 {len(chunks)} 个时间戳块")
            
            # 显示前几个词的时间戳
            print("\n前10个词的时间戳:")
            for i, chunk in enumerate(chunks[:10]):
                if 'timestamp' in chunk:
                    start_time, end_time = chunk['timestamp']
                    print(f"  {i+1:2d}. '{chunk['text']}' [{start_time:.3f}s - {end_time:.3f}s]")
                else:
                    print(f"  {i+1:2d}. '{chunk['text']}' [无时间戳]")
        else:
            print("❌ 未检测到词级别时间戳数据")
        
        # 保存结果
        output_file_no_prep = "output/test_word_timestamps_no_preprocessing.json"
        os.makedirs("output", exist_ok=True)
        with open(output_file_no_prep, 'w', encoding='utf-8') as f:
            json.dump(result_no_prep, f, ensure_ascii=False, indent=2)
        print(f"💾 结果已保存到: {output_file_no_prep}")
        
        # 2. 测试启用预处理的词级别时间戳
        print("\n" + "=" * 40)
        print("测试2: 启用预处理的词级别时间戳")
        print("=" * 40)
        
        transcriber_with_prep = WhisperTranscriber(enable_preprocessing=True)
        
        result_with_prep = transcriber_with_prep.transcribe(
            audio_file,
            return_timestamps=True,
            timestamp_granularity="word"
        )
        
        print(f"✅ 转录完成 (启用预处理)")
        print(f"📝 转录文本: {result_with_prep['text'][:100]}...")
        print(f"⏱️  总处理时间: {result_with_prep['processing_time']:.2f}秒")
        if 'preprocessing_time' in result_with_prep:
            print(f"⏱️  预处理时间: {result_with_prep['preprocessing_time']:.2f}秒")
        if 'transcription_time' in result_with_prep:
            print(f"⏱️  转录时间: {result_with_prep['transcription_time']:.2f}秒")
        
        # 检查预处理段的时间戳
        if 'segments' in result_with_prep:
            segments = result_with_prep['segments']
            print(f"📊 检测到 {len(segments)} 个预处理段")
            
            # 显示前几个段的信息
            print("\n前5个预处理段:")
            for i, segment in enumerate(segments[:5]):
                print(f"  {i+1:2d}. [{segment['startTime']:.3f}s - {segment['endTime']:.3f}s] '{segment['sentence'][:50]}...'")
        
        # 保存结果
        output_file_with_prep = "output/test_word_timestamps_with_preprocessing.json"
        with open(output_file_with_prep, 'w', encoding='utf-8') as f:
            json.dump(result_with_prep, f, ensure_ascii=False, indent=2)
        print(f"💾 结果已保存到: {output_file_with_prep}")
        
        # 3. 验证词级别时间戳的质量
        print("\n" + "=" * 40)
        print("测试3: 词级别时间戳质量验证")
        print("=" * 40)
        
        success = True
        
        # 验证无预处理结果
        if 'chunks' in result_no_prep:
            chunks = result_no_prep['chunks']
            word_count = len(chunks)
            timestamp_count = sum(1 for chunk in chunks if 'timestamp' in chunk and chunk['timestamp'] is not None)
            
            print(f"📊 无预处理结果:")
            print(f"   - 总词数: {word_count}")
            print(f"   - 有时间戳的词数: {timestamp_count}")
            print(f"   - 时间戳覆盖率: {timestamp_count/word_count*100:.1f}%")
            
            if timestamp_count == 0:
                print("❌ 无预处理模式下未检测到任何词级别时间戳")
                success = False
            elif timestamp_count / word_count < 0.8:
                print("⚠️  无预处理模式下词级别时间戳覆盖率较低")
            else:
                print("✅ 无预处理模式下词级别时间戳正常")
        else:
            print("❌ 无预处理结果中缺少chunks数据")
            success = False
        
        # 验证预处理结果
        if 'segments' in result_with_prep:
            segments = result_with_prep['segments']
            segment_count = len(segments)
            valid_segments = sum(1 for seg in segments if seg.get('sentence', '').strip())
            
            print(f"\n📊 预处理结果:")
            print(f"   - 总段数: {segment_count}")
            print(f"   - 有效段数: {valid_segments}")
            print(f"   - 有效段比例: {valid_segments/segment_count*100:.1f}%")
            
            if valid_segments == 0:
                print("❌ 预处理模式下未检测到任何有效转录段")
                success = False
            elif valid_segments / segment_count < 0.5:
                print("⚠️  预处理模式下有效转录段比例较低")
            else:
                print("✅ 预处理模式下转录段正常")
        else:
            print("❌ 预处理结果中缺少segments数据")
            success = False
        
        # 4. 生成测试报告
        print("\n" + "=" * 40)
        print("测试报告")
        print("=" * 40)
        
        report = {
            "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "audio_file": audio_file,
            "tests": {
                "no_preprocessing": {
                    "success": 'chunks' in result_no_prep,
                    "word_count": len(result_no_prep.get('chunks', [])),
                    "timestamp_count": sum(1 for chunk in result_no_prep.get('chunks', []) 
                                         if 'timestamp' in chunk and chunk['timestamp'] is not None),
                    "processing_time": result_no_prep['processing_time']
                },
                "with_preprocessing": {
                    "success": 'segments' in result_with_prep,
                    "segment_count": len(result_with_prep.get('segments', [])),
                    "valid_segments": sum(1 for seg in result_with_prep.get('segments', []) 
                                        if seg.get('sentence', '').strip()),
                    "processing_time": result_with_prep['processing_time']
                }
            },
            "overall_success": success
        }
        
        # 保存测试报告
        report_file = "output/word_timestamps_test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        if success:
            print("✅ 所有测试通过！词级别时间戳功能正常工作")
        else:
            print("❌ 部分测试失败，词级别时间戳功能存在问题")
        
        print(f"📋 详细测试报告已保存到: {report_file}")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始词级别时间戳功能测试...")
    
    success = test_word_timestamps()
    
    if success:
        print("\n🎉 测试完成！词级别时间戳功能正常")
        sys.exit(0)
    else:
        print("\n💥 测试失败！需要修复词级别时间戳功能")
        sys.exit(1)


if __name__ == "__main__":
    main() 