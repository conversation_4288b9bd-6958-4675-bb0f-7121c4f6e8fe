#!/usr/bin/env python3
"""
最终模块隔离测试
验证 Whisper 和 Qwen 模块的完全隔离
"""

import sys
import os
from pathlib import Path

def test_directory_structure():
    """测试目录结构"""
    print("🔍 测试目录结构")
    print("-" * 40)
    
    # 检查共享模块是否移到主目录
    shared_modules = ["core", "utils", "cli"]
    for module in shared_modules:
        if Path(module).exists():
            print(f"✅ {module} 模块在主目录")
        else:
            print(f"❌ {module} 模块不在主目录")
            return False
    
    # 检查 audio_processor 中是否还有这些模块
    for module in shared_modules:
        if Path(f"audio_processor/{module}").exists():
            print(f"⚠️  audio_processor/{module} 仍然存在")
        else:
            print(f"✅ audio_processor/{module} 已移除")
    
    # 检查 models 目录是否已删除
    if not Path("audio_processor/models").exists():
        print("✅ audio_processor/models 目录已删除")
    else:
        print("⚠️  audio_processor/models 目录仍然存在")
    
    # 检查 qwen 模块是否存在
    if Path("qwen").exists():
        print("✅ qwen 模块目录存在")
        
        # 检查关键文件
        key_files = [
            "qwen/__init__.py",
            "qwen/__main__.py", 
            "qwen/processor.py",
            "qwen/cli.py",
            "qwen/api/server.py",
            "qwen/run_qwen.sh",
            "qwen/run_qwen.py"
        ]
        
        for file_path in key_files:
            if Path(file_path).exists():
                print(f"✅ {file_path} 存在")
            else:
                print(f"❌ {file_path} 不存在")
                return False
    else:
        print("❌ qwen 模块目录不存在")
        return False
    
    return True

def test_import_isolation():
    """测试导入隔离"""
    print("\n🔍 测试导入隔离")
    print("-" * 40)
    
    try:
        # 测试 audio_processor 主模块
        print("📋 导入 audio_processor...")
        import audio_processor
        print("✅ audio_processor 导入成功")
        
        # 检查可用组件
        available_components = audio_processor.__all__
        print(f"📋 可用组件: {available_components}")
        
        # 验证 QwenProcessor 不在主模块中
        if "QwenProcessor" not in available_components:
            print("✅ QwenProcessor 已从主模块中移除")
        else:
            print("⚠️  QwenProcessor 仍在主模块中")
            return False
        
        # 验证转录组件可用
        if "WhisperTranscriber" in available_components:
            print("✅ WhisperTranscriber 在主模块中可用")
        else:
            print("❌ WhisperTranscriber 不在主模块中")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def test_qwen_isolation():
    """测试 Qwen 模块隔离"""
    print("\n🔍 测试 Qwen 模块隔离")
    print("-" * 40)
    
    try:
        # 测试 qwen 模块导入
        print("📋 导入 qwen 模块...")
        import qwen
        print("✅ qwen 模块导入成功")
        
        # 检查可用性
        available = qwen.is_available()
        print(f"📋 Qwen 可用性: {available}")
        
        if not available:
            error = qwen.get_import_error()
            print(f"📋 导入错误: {error}")
            print("✅ 这是预期的，因为当前环境没有安装 llama-cpp-python")
        
        return True
        
    except Exception as e:
        print(f"❌ Qwen 模块测试失败: {e}")
        return False

def test_environment_scripts():
    """测试环境切换脚本"""
    print("\n🔍 测试环境切换脚本")
    print("-" * 40)
    
    scripts = [
        "qwen/run_qwen.sh",
        "qwen/run_qwen.py"
    ]
    
    for script in scripts:
        script_path = Path(script)
        if script_path.exists():
            print(f"✅ {script} 存在")
            
            # 检查是否可执行
            if os.access(script_path, os.X_OK):
                print(f"✅ {script} 可执行")
            else:
                print(f"⚠️  {script} 不可执行")
        else:
            print(f"❌ {script} 不存在")
            return False
    
    return True

def test_cli_separation():
    """测试 CLI 分离"""
    print("\n🔍 测试 CLI 分离")
    print("-" * 40)
    
    # 检查主 CLI 是否移除了 qwen 相关代码
    main_cli_path = Path("cli/main.py")
    if main_cli_path.exists():
        with open(main_cli_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "qwen_cli" not in content:
            print("✅ 主 CLI 已移除 qwen_cli 导入")
        else:
            print("⚠️  主 CLI 仍包含 qwen_cli 引用")
        
        if "qwen" not in content.lower() or "注意: Qwen 文本生成功能已移动到独立的 qwen 模块" in content:
            print("✅ 主 CLI 已移除 qwen 相关代码")
        else:
            print("⚠️  主 CLI 仍包含 qwen 相关代码")
    
    # 检查 qwen CLI 是否独立
    qwen_cli_path = Path("qwen/cli.py")
    if qwen_cli_path.exists():
        print("✅ qwen CLI 已独立")
    else:
        print("❌ qwen CLI 不存在")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 最终模块隔离测试")
    print("🎯 验证 Whisper 和 Qwen 模块的完全隔离")
    print("=" * 70)
    
    tests = []
    
    # 1. 测试目录结构
    if test_directory_structure():
        tests.append("目录结构")
    
    # 2. 测试导入隔离
    if test_import_isolation():
        tests.append("导入隔离")
    
    # 3. 测试 Qwen 模块隔离
    if test_qwen_isolation():
        tests.append("Qwen 模块隔离")
    
    # 4. 测试环境切换脚本
    if test_environment_scripts():
        tests.append("环境切换脚本")
    
    # 5. 测试 CLI 分离
    if test_cli_separation():
        tests.append("CLI 分离")
    
    # 总结
    print("\n" + "=" * 70)
    print(f"📊 测试结果: {len(tests)}/5 项测试通过")
    print("📋 通过的测试:")
    for test in tests:
        print(f"   ✅ {test}")
    
    if len(tests) == 5:
        print("\n🎉 模块隔离重构成功！")
        print("✅ Whisper 和 Qwen 模块已完全隔离")
        print("✅ 共享模块已移到主目录")
        print("✅ 环境自动切换脚本已创建")
        print("✅ CLI 已正确分离")
        
        print("\n📋 新的项目结构:")
        print("项目根目录/")
        print("├── core/                    # 共享核心模块")
        print("├── utils/                   # 共享工具模块")
        print("├── cli/                     # 主 CLI (仅 Whisper)")
        print("├── audio_processor/         # 音频转录模块")
        print("│   ├── transcribing/")
        print("│   └── preprocessing/")
        print("└── qwen/                    # 独立 Qwen 模块")
        print("    ├── processor.py")
        print("    ├── cli.py")
        print("    ├── api/")
        print("    ├── run_qwen.sh         # 自动环境切换脚本")
        print("    └── run_qwen.py         # Python 环境切换脚本")
        
        print("\n📋 使用方法:")
        print("Whisper 转录 (camel_tools_env):")
        print("  conda activate camel_tools_env")
        print("  python -m cli.main whisper audio.wav")
        print("")
        print("Qwen 文本生成 (audio_processor_env):")
        print("  ./qwen/run_qwen.sh --port 8000")
        print("  # 或者")
        print("  python qwen/run_qwen.py --port 8000")
        
        return True
    else:
        print("\n⚠️  部分测试失败")
        print("💡 需要进一步检查和修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
