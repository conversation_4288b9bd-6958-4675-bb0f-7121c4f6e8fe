#!/usr/bin/env python3
"""
设置兼容的 OpenAI Whisper 环境
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description=""):
    """运行命令并显示结果"""
    if description:
        print(f"📋 {description}")
    
    print(f"🔧 执行: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
        else:
            print("❌ 失败")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

def create_whisper_env():
    """创建兼容的 Whisper 环境"""
    print("🚀 创建兼容的 OpenAI Whisper 环境")
    print("=" * 60)
    
    env_name = "whisper_compatible_env"
    
    # 1. 创建新环境（Python 3.11）
    if not run_command(
        f"conda create -n {env_name} python=3.11 -y",
        f"创建新环境 {env_name} (Python 3.11)"
    ):
        return False
    
    # 2. 安装基础包
    commands = [
        # PyTorch 和相关包
        f"conda activate {env_name} && conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia -y",
        
        # 音频处理包
        f"conda activate {env_name} && conda install ffmpeg -c conda-forge -y",
        f"conda activate {env_name} && pip install librosa soundfile",
        
        # OpenAI Whisper 和兼容版本
        f"conda activate {env_name} && pip install openai-whisper==20231117",  # 使用稳定版本
        
        # 科学计算包
        f"conda activate {env_name} && pip install numpy scipy pandas",
        
        # 其他必要包
        f"conda activate {env_name} && pip install pyyaml loguru pathlib2",
        f"conda activate {env_name} && pip install tqdm requests",
        
        # 音频预处理相关
        f"conda activate {env_name} && pip install webrtcvad",
        
        # 可选：如果需要 DeepFilterNet
        f"conda activate {env_name} && pip install deepfilternet",
    ]
    
    for i, cmd in enumerate(commands, 1):
        if not run_command(cmd, f"安装包 {i}/{len(commands)}"):
            print(f"⚠️  包 {i} 安装失败，继续下一个...")
    
    return True

def test_whisper_in_new_env():
    """在新环境中测试 Whisper"""
    print("\n🔍 在新环境中测试 Whisper")
    print("-" * 40)
    
    env_name = "whisper_compatible_env"
    
    test_commands = [
        # 测试基本导入
        f"conda activate {env_name} && python -c \"import whisper; print('✅ Whisper 导入成功')\"",
        
        # 测试模型加载
        f"conda activate {env_name} && python -c \"import whisper; model = whisper.load_model('tiny'); print('✅ 模型加载成功')\"",
        
        # 测试 turbo 模型
        f"conda activate {env_name} && python -c \"import whisper; model = whisper.load_model('turbo'); print('✅ Turbo 模型加载成功')\"",
    ]
    
    for i, cmd in enumerate(test_commands, 1):
        if run_command(cmd, f"测试 {i}/{len(test_commands)}"):
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
            return False
    
    return True

def create_activation_script():
    """创建环境激活脚本"""
    print("\n📝 创建环境激活脚本")
    
    script_content = f"""#!/bin/bash
# OpenAI Whisper 兼容环境激活脚本

echo "🚀 激活 OpenAI Whisper 兼容环境..."
conda activate whisper_compatible_env

echo "📋 环境信息:"
echo "Python 版本: $(python --version)"
echo "Conda 环境: $CONDA_DEFAULT_ENV"

echo "🔍 验证 Whisper 安装:"
python -c "import whisper; print('✅ Whisper 版本:', whisper.__version__)"

echo "💡 使用方法:"
echo "cd /home/<USER>/audio_processor"
echo "python test_real_audio_final.py"

echo "✅ 环境准备完成！"
"""
    
    script_path = Path("activate_whisper_env.sh")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # 设置执行权限
    os.chmod(script_path, 0o755)
    
    print(f"✅ 激活脚本已创建: {script_path}")
    return True

def create_test_script():
    """创建测试脚本"""
    print("\n📝 创建测试脚本")
    
    test_content = """#!/usr/bin/env python3
'''
在兼容环境中测试 OpenAI Whisper
'''

import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '/home/<USER>/audio_processor')

def test_whisper():
    print("🔍 测试 OpenAI Whisper")
    print("-" * 40)
    
    try:
        import whisper
        print(f"✅ Whisper 版本: {whisper.__version__}")
        
        # 测试模型加载
        print("📋 加载 turbo 模型...")
        model = whisper.load_model("turbo")
        print("✅ Turbo 模型加载成功")
        
        # 测试音频文件
        audio_file = "/home/<USER>/audio_processor/data/samples/过量（二）.mp3"
        if Path(audio_file).exists():
            print(f"📋 测试音频: {Path(audio_file).name}")
            
            print("📋 开始转录...")
            start_time = time.time()
            result = model.transcribe(
                audio_file,
                language="zh",
                task="transcribe",
                verbose=True,
                word_timestamps=True,
                condition_on_previous_text=True,
                temperature=0.0
            )
            transcribe_time = time.time() - start_time
            
            print(f"✅ 转录完成 (耗时: {transcribe_time:.2f}秒)")
            print(f"📋 转录文本: {result['text'][:100]}...")
            print(f"📋 段落数量: {len(result['segments'])}")
            
            # 检查词级时间戳
            if result['segments'] and 'words' in result['segments'][0]:
                words = result['segments'][0]['words']
                print(f"✅ 词级时间戳: {len(words)}个词")
            else:
                print("⚠️  未找到词级时间戳")
            
            return True
        else:
            print(f"❌ 音频文件不存在: {audio_file}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_project_integration():
    print("\\n🔍 测试项目集成")
    print("-" * 40)
    
    try:
        from audio_processor.transcribing import WhisperTranscriber
        print("✅ 项目转录器导入成功")
        
        # 创建转录器
        transcriber = WhisperTranscriber("whisper", enable_preprocessing=False)
        print("✅ 转录器创建成功")
        
        # 检查配置
        model_name = transcriber.get_config_value("model.name", "unknown")
        print(f"📋 配置模型: {model_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 项目集成测试失败: {e}")
        return False

def main():
    print("🚀 OpenAI Whisper 兼容环境测试")
    print("=" * 50)
    
    tests = []
    
    if test_whisper():
        tests.append("Whisper 基本功能")
    
    if test_project_integration():
        tests.append("项目集成")
    
    print("\\n" + "=" * 50)
    print(f"📊 测试结果: {len(tests)}/2 通过")
    
    if len(tests) == 2:
        print("🎉 所有测试通过！环境设置成功")
        return True
    else:
        print("⚠️  部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
"""
    
    test_path = Path("test_whisper_compatible.py")
    with open(test_path, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"✅ 测试脚本已创建: {test_path}")
    return True

def main():
    """主函数"""
    print("🚀 设置兼容的 OpenAI Whisper 环境")
    print("=" * 60)
    
    # 1. 创建环境
    if not create_whisper_env():
        print("❌ 环境创建失败")
        return False
    
    # 2. 测试环境
    if not test_whisper_in_new_env():
        print("❌ 环境测试失败")
        return False
    
    # 3. 创建辅助脚本
    create_activation_script()
    create_test_script()
    
    print("\n🎉 兼容环境设置完成！")
    print("\n📋 使用方法:")
    print("1. 激活环境:")
    print("   conda activate whisper_compatible_env")
    print("   # 或者运行: ./activate_whisper_env.sh")
    print("")
    print("2. 测试环境:")
    print("   python test_whisper_compatible.py")
    print("")
    print("3. 运行项目测试:")
    print("   python test_real_audio_final.py")
    print("")
    print("✅ 现在可以在兼容环境中正常使用 OpenAI Whisper 了！")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
