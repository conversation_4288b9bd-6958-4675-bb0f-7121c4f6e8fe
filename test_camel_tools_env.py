#!/usr/bin/env python3
"""
在 camel_tools_env 环境中测试和设置 OpenAI Whisper
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def run_in_camel_env(command, description=""):
    """在 camel_tools_env 环境中运行命令"""
    if description:
        print(f"📋 {description}")
    
    full_command = f"conda activate camel_tools_env && {command}"
    print(f"🔧 执行: {command}")
    
    try:
        result = subprocess.run(full_command, shell=True, capture_output=True, text=True, executable='/bin/bash')
        
        if result.returncode == 0:
            print("✅ 成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
            return True, result.stdout
        else:
            print("❌ 失败")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
            return False, result.stderr
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False, str(e)

def check_camel_env():
    """检查 camel_tools_env 环境"""
    print("🔍 检查 camel_tools_env 环境")
    print("-" * 40)
    
    # 检查 Python 版本
    success, output = run_in_camel_env("python --version", "检查 Python 版本")
    if not success:
        return False
    
    # 检查是否已安装 whisper
    success, output = run_in_camel_env("python -c \"import whisper; print('Whisper 已安装')\"", "检查 Whisper 安装状态")
    if success:
        print("✅ Whisper 已经安装")
        return True
    else:
        print("📋 Whisper 未安装，需要安装")
        return False

def install_whisper_in_camel_env():
    """在 camel_tools_env 中安装 OpenAI Whisper"""
    print("\n🔧 在 camel_tools_env 中安装 OpenAI Whisper")
    print("-" * 40)
    
    # 安装 openai-whisper
    success, output = run_in_camel_env("pip install openai-whisper", "安装 openai-whisper")
    if not success:
        print("❌ openai-whisper 安装失败")
        return False
    
    # 安装音频处理依赖
    dependencies = [
        "librosa",
        "soundfile", 
        "ffmpeg-python"
    ]
    
    for dep in dependencies:
        success, output = run_in_camel_env(f"pip install {dep}", f"安装 {dep}")
        if not success:
            print(f"⚠️  {dep} 安装失败，继续...")
    
    return True

def test_whisper_in_camel_env():
    """在 camel_tools_env 中测试 Whisper"""
    print("\n🔍 在 camel_tools_env 中测试 Whisper")
    print("-" * 40)
    
    # 测试基本导入
    success, output = run_in_camel_env("python -c \"import whisper; print('✅ Whisper 导入成功')\"", "测试 Whisper 导入")
    if not success:
        return False
    
    # 测试模型加载
    success, output = run_in_camel_env("python -c \"import whisper; model = whisper.load_model('tiny'); print('✅ Tiny 模型加载成功')\"", "测试 Tiny 模型加载")
    if not success:
        return False
    
    # 测试 turbo 模型
    success, output = run_in_camel_env("python -c \"import whisper; model = whisper.load_model('turbo'); print('✅ Turbo 模型加载成功')\"", "测试 Turbo 模型加载")
    if not success:
        print("⚠️  Turbo 模型加载失败，但 Tiny 模型可用")
        return True  # Tiny 模型可用就算成功
    
    return True

def test_real_audio_in_camel_env():
    """在 camel_tools_env 中测试真实音频转录"""
    print("\n🔍 在 camel_tools_env 中测试真实音频转录")
    print("-" * 40)
    
    # 检查音频文件
    audio_file = "/home/<USER>/audio_processor/data/samples/过量（二）.mp3"
    if not Path(audio_file).exists():
        print(f"❌ 音频文件不存在: {audio_file}")
        return False
    
    print(f"📋 音频文件: {Path(audio_file).name}")
    
    # 创建测试脚本
    test_script = f"""
import sys
sys.path.insert(0, '/home/<USER>/audio_processor')
import whisper
import time

print('📋 加载 turbo 模型...')
model = whisper.load_model('turbo')
print('✅ 模型加载成功')

print('📋 开始转录...')
start_time = time.time()
result = model.transcribe(
    '{audio_file}',
    language='zh',
    task='transcribe',
    verbose=True,
    word_timestamps=True,
    condition_on_previous_text=True,
    temperature=0.0
)
transcribe_time = time.time() - start_time

print(f'✅ 转录完成 (耗时: {{transcribe_time:.2f}}秒)')
print(f'📋 转录文本: {{result["text"][:100]}}...')
print(f'📋 段落数量: {{len(result["segments"])}}')

# 检查词级时间戳
if result['segments'] and 'words' in result['segments'][0]:
    words = result['segments'][0]['words']
    print(f'✅ 词级时间戳: {{len(words)}}个词')
    # 显示前3个词
    for i, word in enumerate(words[:3]):
        print(f'   {{i+1}}. {{word["word"]}}: [{{word["start"]:.2f}} --> {{word["end"]:.2f}}]')
else:
    print('⚠️  未找到词级时间戳')

print('🎉 真实音频转录测试成功！')
"""
    
    # 将测试脚本写入临时文件
    temp_script = Path("/tmp/test_whisper_audio.py")
    with open(temp_script, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    # 运行测试脚本
    success, output = run_in_camel_env(f"python {temp_script}", "测试真实音频转录")
    
    # 清理临时文件
    if temp_script.exists():
        temp_script.unlink()
    
    return success

def test_project_integration_in_camel_env():
    """在 camel_tools_env 中测试项目集成"""
    print("\n🔍 在 camel_tools_env 中测试项目集成")
    print("-" * 40)
    
    # 创建项目集成测试脚本
    integration_script = f"""
import sys
sys.path.insert(0, '/home/<USER>/audio_processor')

try:
    from audio_processor.transcribing import WhisperTranscriber
    print('✅ 项目转录器导入成功')
    
    # 创建转录器
    transcriber = WhisperTranscriber("whisper", enable_preprocessing=False)
    print('✅ 转录器创建成功')
    
    # 检查配置
    model_name = transcriber.get_config_value("model.name", "unknown")
    print(f'📋 配置模型: {{model_name}}')
    
    # 测试音频转录
    audio_file = '/home/<USER>/audio_processor/data/samples/过量（二）.mp3'
    result = transcriber.transcribe(
        audio_file,
        language='zh',
        word_timestamps=True,
        temperature=0.0
    )
    
    print(f'✅ 项目转录器转录成功')
    print(f'📋 转录文本: {{result["text"][:100]}}...')
    print(f'📋 处理时间: {{result.get("processing_time", "N/A"):.2f}}秒')
    print(f'📋 模型: {{result.get("model", "N/A")}}')
    
    # 检查分段
    chunks = result.get('chunks', [])
    if chunks:
        print(f'📋 分段数量: {{len(chunks)}}')
        if 'words' in chunks[0]:
            print(f'✅ 词级时间戳正常工作')
        else:
            print('⚠️  词级时间戳可能未启用')
    
    print('🎉 项目集成测试成功！')
    
except Exception as e:
    print(f'❌ 项目集成测试失败: {{e}}')
    import traceback
    traceback.print_exc()
"""
    
    # 将测试脚本写入临时文件
    temp_script = Path("/tmp/test_project_integration.py")
    with open(temp_script, 'w', encoding='utf-8') as f:
        f.write(integration_script)
    
    # 运行测试脚本
    success, output = run_in_camel_env(f"python {temp_script}", "测试项目集成")
    
    # 清理临时文件
    if temp_script.exists():
        temp_script.unlink()
    
    return success

def main():
    """主函数"""
    print("🚀 在 camel_tools_env 中设置和测试 OpenAI Whisper")
    print("=" * 60)
    
    # 1. 检查环境
    whisper_installed = check_camel_env()
    
    # 2. 如果未安装，则安装 Whisper
    if not whisper_installed:
        if not install_whisper_in_camel_env():
            print("❌ Whisper 安装失败")
            return False
    
    # 3. 测试 Whisper 基本功能
    if not test_whisper_in_camel_env():
        print("❌ Whisper 基本功能测试失败")
        return False
    
    # 4. 测试真实音频转录
    if not test_real_audio_in_camel_env():
        print("❌ 真实音频转录测试失败")
        return False
    
    # 5. 测试项目集成
    if not test_project_integration_in_camel_env():
        print("❌ 项目集成测试失败")
        return False
    
    print("\n🎉 所有测试通过！")
    print("✅ camel_tools_env 环境中的 OpenAI Whisper 设置成功")
    print("✅ 真实音频转录功能正常")
    print("✅ 项目集成正常")
    print("✅ 词级别时间戳功能正常")
    
    print("\n📋 使用方法:")
    print("conda activate camel_tools_env")
    print("cd /home/<USER>/audio_processor")
    print("python test_real_audio_final.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
