#!/usr/bin/env python3
"""
转录模块功能测试脚本
测试从音频预处理到转录的完整流程，重点关注词级别时间戳
"""

import os
import sys
import json
import time
import subprocess
from pathlib import Path
from typing import Dict, Any, List

class TranscriptionTester:
    def __init__(self):
        self.test_audio_file = "data/samples/过量（二）.mp3"
        self.output_dir = Path("output")
        self.output_dir.mkdir(exist_ok=True)
        
    def check_environment(self) -> bool:
        """检查camel_tools_env环境是否存在"""
        try:
            result = subprocess.run(
                ["conda", "env", "list"], 
                capture_output=True, 
                text=True, 
                check=True
            )
            
            if "camel_tools_env" in result.stdout:
                print("✅ camel_tools_env 环境存在")
                return True
            else:
                print("❌ camel_tools_env 环境不存在")
                return False
                
        except Exception as e:
            print(f"❌ 检查环境失败: {e}")
            return False
    
    def check_audio_file(self) -> bool:
        """检查测试音频文件是否存在"""
        if Path(self.test_audio_file).exists():
            print(f"✅ 测试音频文件存在: {self.test_audio_file}")
            return True
        else:
            print(f"❌ 测试音频文件不存在: {self.test_audio_file}")
            return False
    
    def test_preprocessing_pipeline(self) -> bool:
        """测试音频预处理流程"""
        print("\n🔍 测试音频预处理流程...")
        
        try:
            # 切换到camel_tools_env环境并运行预处理
            cmd = [
                "conda", "run", "-n", "camel_tools_env",
                "python", "-c", f"""
import sys
sys.path.append('.')
from audio_processor.preprocessing.processor import AudioPreprocessor
import json

# 初始化预处理器
preprocessor = AudioPreprocessor()

# 处理音频文件
result = preprocessor.process_audio('{self.test_audio_file}')

# 保存结果
with open('output/preprocessing_result.json', 'w', encoding='utf-8') as f:
    json.dump(result, f, ensure_ascii=False, indent=2)

print("预处理完成")
print(f"处理的段数: {{len(result.get('segments', []))}}")
"""
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print("✅ 音频预处理成功")
                print(f"   输出: {result.stdout.strip()}")
                
                # 检查输出文件
                output_file = self.output_dir / "preprocessing_result.json"
                if output_file.exists():
                    with open(output_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"   处理的音频段数: {len(data.get('segments', []))}")
                    return True
                else:
                    print("❌ 预处理输出文件未生成")
                    return False
            else:
                print(f"❌ 音频预处理失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 音频预处理异常: {e}")
            return False
    
    def test_whisper_transcription(self) -> bool:
        """测试Whisper转录功能，重点测试词级别时间戳"""
        print("\n🔍 测试Whisper转录功能（词级别时间戳）...")
        
        try:
            # 切换到camel_tools_env环境并运行转录
            cmd = [
                "conda", "run", "-n", "camel_tools_env",
                "python", "-c", f"""
import sys
sys.path.append('.')
from audio_processor.transcribing.whisper_original import OriginalWhisperTranscriber
import json

# 初始化Whisper转录器
transcriber = OriginalWhisperTranscriber()

# 转录音频文件，启用词级别时间戳
result = transcriber.transcribe(
    audio_path='{self.test_audio_file}',
    word_timestamps=True,
    language='zh',
    model_size='large-v3-turbo'
)

# 保存结果
with open('output/transcription_result.json', 'w', encoding='utf-8') as f:
    json.dump(result, f, ensure_ascii=False, indent=2)

print("转录完成")
print(f"转录文本: {{result.get('text', '')[:100]}}...")
print(f"段数: {{len(result.get('segments', []))}}")

# 检查词级别时间戳
segments = result.get('segments', [])
if segments:
    first_segment = segments[0]
    words = first_segment.get('words', [])
    print(f"第一段词数: {{len(words)}}")
    if words:
        print(f"第一个词: {{words[0]}}")
"""
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("✅ Whisper转录成功")
                print(f"   输出: {result.stdout.strip()}")
                
                # 检查输出文件和词级别时间戳
                output_file = self.output_dir / "transcription_result.json"
                if output_file.exists():
                    with open(output_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    text = data.get('text', '')
                    segments = data.get('segments', [])
                    
                    print(f"   转录文本: {text[:100]}...")
                    print(f"   段数: {len(segments)}")
                    
                    # 检查词级别时间戳
                    word_timestamps_found = False
                    total_words = 0
                    
                    for segment in segments:
                        words = segment.get('words', [])
                        total_words += len(words)
                        if words:
                            word_timestamps_found = True
                            break
                    
                    if word_timestamps_found:
                        print(f"   ✅ 词级别时间戳正常工作，总词数: {total_words}")
                        
                        # 显示第一个词的详细信息
                        if segments and segments[0].get('words'):
                            first_word = segments[0]['words'][0]
                            print(f"   第一个词示例: {first_word}")
                        
                        return True
                    else:
                        print("   ❌ 未找到词级别时间戳")
                        return False
                else:
                    print("❌ 转录输出文件未生成")
                    return False
            else:
                print(f"❌ Whisper转录失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Whisper转录异常: {e}")
            return False
    
    def test_complete_pipeline(self) -> bool:
        """测试完整的音频处理流程"""
        print("\n🔍 测试完整音频处理流程...")
        
        try:
            # 切换到camel_tools_env环境并运行完整流程
            cmd = [
                "conda", "run", "-n", "camel_tools_env",
                "python", "-c", f"""
import sys
sys.path.append('.')
from audio_processor.preprocessing.processor import AudioPreprocessor
from audio_processor.transcribing.whisper_original import OriginalWhisperTranscriber
import json
import time

start_time = time.time()

# 1. 音频预处理
print("开始音频预处理...")
preprocessor = AudioPreprocessor()
preprocessing_result = preprocessor.process_audio('{self.test_audio_file}')
preprocessing_time = time.time() - start_time
print(f"预处理完成，耗时: {{preprocessing_time:.2f}}秒")

# 2. Whisper转录
print("开始Whisper转录...")
transcription_start = time.time()
transcriber = OriginalWhisperTranscriber()
transcription_result = transcriber.transcribe(
    audio_path='{self.test_audio_file}',
    word_timestamps=True,
    language='zh',
    model_size='large-v3-turbo'
)
transcription_time = time.time() - transcription_start
total_time = time.time() - start_time

# 3. 合并结果
complete_result = {{
    'preprocessing': preprocessing_result,
    'transcription': transcription_result,
    'timing': {{
        'preprocessing_time': preprocessing_time,
        'transcription_time': transcription_time,
        'total_time': total_time
    }}
}}

# 保存完整结果
with open('output/complete_pipeline_result.json', 'w', encoding='utf-8') as f:
    json.dump(complete_result, f, ensure_ascii=False, indent=2)

print(f"完整流程完成，总耗时: {{total_time:.2f}}秒")
print(f"转录文本: {{transcription_result.get('text', '')[:100]}}...")
"""
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print("✅ 完整音频处理流程成功")
                print(f"   输出: {result.stdout.strip()}")
                
                # 检查输出文件
                output_file = self.output_dir / "complete_pipeline_result.json"
                if output_file.exists():
                    with open(output_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    timing = data.get('timing', {})
                    transcription = data.get('transcription', {})
                    
                    print(f"   预处理耗时: {timing.get('preprocessing_time', 0):.2f}秒")
                    print(f"   转录耗时: {timing.get('transcription_time', 0):.2f}秒")
                    print(f"   总耗时: {timing.get('total_time', 0):.2f}秒")
                    print(f"   转录文本: {transcription.get('text', '')[:100]}...")
                    
                    return True
                else:
                    print("❌ 完整流程输出文件未生成")
                    return False
            else:
                print(f"❌ 完整音频处理流程失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 完整音频处理流程异常: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有转录模块测试"""
        print("🧪 开始转录模块功能测试")
        print("=" * 60)
        
        tests = {
            "环境检查": self.check_environment,
            "音频文件检查": self.check_audio_file,
            "音频预处理": self.test_preprocessing_pipeline,
            "Whisper转录(词级时间戳)": self.test_whisper_transcription,
            "完整处理流程": self.test_complete_pipeline
        }
        
        results = {}
        for test_name, test_func in tests.items():
            print(f"\n🔍 测试: {test_name}")
            print("-" * 40)
            results[test_name] = test_func()
            time.sleep(1)  # 避免测试过快
        
        print("\n" + "=" * 60)
        print("📊 转录模块测试结果汇总:")
        passed = 0
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{len(tests)} 个测试通过")
        return results

if __name__ == "__main__":
    tester = TranscriptionTester()
    results = tester.run_all_tests()
    
    # 输出详细的测试报告
    if all(results.values()):
        print("\n🎉 所有转录模块测试通过！")
        print("转录模块功能正常，词级别时间戳工作正常。")
    else:
        print("\n⚠️  部分转录模块测试失败")
        failed_tests = [name for name, result in results.items() if not result]
        print(f"失败的测试: {', '.join(failed_tests)}")
