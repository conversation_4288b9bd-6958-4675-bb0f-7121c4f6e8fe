#!/usr/bin/env python3
"""
测试 Qwen 模块与转录模块的完全隔离
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_transcribing_isolation():
    """测试转录模块是否完全独立"""
    print("🔍 测试转录模块隔离")
    print("-" * 40)
    
    try:
        # 测试转录模块导入
        print("📋 导入转录模块...")
        from audio_processor.transcribing import WhisperTranscriber
        print("✅ WhisperTranscriber 导入成功")
        
        # 测试转录器创建
        print("📋 创建转录器...")
        transcriber = WhisperTranscriber("whisper", enable_preprocessing=False)
        print("✅ 转录器创建成功")
        
        # 检查配置
        model_name = transcriber.get_config_value("model.name", "unknown")
        print(f"📋 配置模型: {model_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 转录模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_qwen_isolation():
    """测试 Qwen 模块是否完全独立"""
    print("\n🔍 测试 Qwen 模块隔离")
    print("-" * 40)
    
    try:
        # 测试 qwen 模块导入
        print("📋 导入 qwen 模块...")
        import qwen
        print("✅ qwen 模块导入成功")
        
        # 检查可用性
        available = qwen.is_available()
        print(f"📋 Qwen 可用性: {available}")
        
        if not available:
            error = qwen.get_import_error()
            print(f"📋 导入错误: {error}")
            print("✅ 这是预期的，因为我们没有安装 llama-cpp-python")
        
        return True
        
    except Exception as e:
        print(f"❌ Qwen 模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_audio_processor_main():
    """测试 audio_processor 主模块"""
    print("\n🔍 测试 audio_processor 主模块")
    print("-" * 40)
    
    try:
        # 测试主模块导入
        print("📋 导入 audio_processor 主模块...")
        import audio_processor
        print("✅ audio_processor 导入成功")
        
        # 检查可用的组件
        available_components = audio_processor.__all__
        print(f"📋 可用组件: {available_components}")
        
        # 验证 QwenProcessor 不在主模块中
        if "QwenProcessor" not in available_components:
            print("✅ QwenProcessor 已从主模块中移除")
        else:
            print("⚠️  QwenProcessor 仍在主模块中")
            return False
        
        # 验证转录组件可用
        if "WhisperTranscriber" in available_components:
            print("✅ WhisperTranscriber 在主模块中可用")
        else:
            print("❌ WhisperTranscriber 不在主模块中")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ audio_processor 主模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_directory_structure():
    """测试目录结构"""
    print("\n🔍 测试目录结构")
    print("-" * 40)
    
    # 检查 models 目录是否已删除
    models_dir = Path("audio_processor/models")
    if not models_dir.exists():
        print("✅ audio_processor/models 目录已删除")
    else:
        print("⚠️  audio_processor/models 目录仍然存在")
    
    # 检查 qwen 模块是否存在
    qwen_dir = Path("qwen")
    if qwen_dir.exists():
        print("✅ qwen 模块目录存在")
        
        # 检查关键文件
        key_files = [
            "qwen/__init__.py",
            "qwen/__main__.py", 
            "qwen/processor.py",
            "qwen/cli.py",
            "qwen/api/server.py"
        ]
        
        for file_path in key_files:
            if Path(file_path).exists():
                print(f"✅ {file_path} 存在")
            else:
                print(f"❌ {file_path} 不存在")
                return False
    else:
        print("❌ qwen 模块目录不存在")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 测试 Qwen 模块与转录模块的完全隔离")
    print("=" * 60)
    
    tests = []
    
    # 1. 测试目录结构
    if test_directory_structure():
        tests.append("目录结构")
    
    # 2. 测试转录模块隔离
    if test_transcribing_isolation():
        tests.append("转录模块隔离")
    
    # 3. 测试 Qwen 模块隔离
    if test_qwen_isolation():
        tests.append("Qwen 模块隔离")
    
    # 4. 测试主模块
    if test_audio_processor_main():
        tests.append("主模块清理")
    
    # 总结
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {len(tests)}/4 项测试通过")
    print("📋 通过的测试:")
    for test in tests:
        print(f"   ✅ {test}")
    
    if len(tests) == 4:
        print("\n🎉 模块隔离成功！")
        print("✅ Qwen 模块已完全从转录逻辑中隔离")
        print("✅ 转录模块可以独立工作")
        print("✅ Qwen 模块作为独立包存在")
        print("✅ 目录结构清理完成")
        
        print("\n📋 使用方法:")
        print("转录功能:")
        print("  from audio_processor.transcribing import WhisperTranscriber")
        print("  transcriber = WhisperTranscriber('whisper')")
        print("")
        print("Qwen 功能:")
        print("  from qwen import QwenProcessor  # 需要安装 llama-cpp-python")
        print("  processor = QwenProcessor()")
        print("")
        print("CLI 使用:")
        print("  python -m audio_processor whisper audio.wav  # 转录")
        print("  python -m qwen.cli --help                     # Qwen API")
        
        return True
    else:
        print("\n⚠️  部分测试失败")
        print("💡 需要进一步检查和修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
