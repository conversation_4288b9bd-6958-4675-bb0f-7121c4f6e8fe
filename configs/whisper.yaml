# Whisper 模型配置 (原始OpenAI Whisper库)

# 模型配置
model:
  name: "turbo"  # 原始OpenAI Whisper支持的模型: tiny, base, small, medium, large, turbo
  device: "cuda"
  torch_dtype: "float16"

# 音频处理配置
audio:
  sample_rate: 16000
  max_duration: 30
  supported_formats: [".wav", ".mp3", ".flac", ".m4a", ".ogg"]

# 转录参数配置 (原始OpenAI Whisper参数)
transcription:
  # 基本参数
  language: null                    # 音频语言 (null=自动检测, "zh", "en", "ar", "ja" 等)
  task: "transcribe"               # 任务类型: "transcribe" 或 "translate"
  initial_prompt: null             # 初始提示词，用于引导模型生成特定风格的文本
  carry_initial_prompt: false      # 是否在每个窗口都使用初始提示词

  # 解码参数
  temperature: 0.0                 # 解码随机性 (0.0=确定性, 可为float或tuple)
  verbose: true                    # 是否显示详细输出

  # 质量控制参数
  compression_ratio_threshold: 2.4 # 压缩比阈值 (用于检测重复)
  logprob_threshold: -1.0          # 对数概率阈值
  no_speech_threshold: 0.6         # 无语音阈值 (0.0-1.0)
  condition_on_previous_text: true # 使用上下文连续性进行转录 (对长音频效果好)

  # 时间戳相关
  word_timestamps: true            # 输出词级时间戳
  prepend_punctuations: "\"'¿([{-" # 前置标点符号
  append_punctuations: "\"'.。,，!！?？:：\")]}、"  # 后置标点符号
  clip_timestamps: "0"             # 时间戳裁剪范围
  hallucination_silence_threshold: null  # 幻觉静音阈值

# 输出配置
output:
  format: "json"  # json, txt, srt
  include_timestamps: true
  include_confidence: false

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file: "logs/whisper.log"
  rotation: "10 MB"
  retention: "7 days"

# 监控配置
monitoring:
  enable_memory_monitoring: true
  enable_gpu_monitoring: true
  log_interval: 10
