#!/usr/bin/env python3
"""
完整转录流程测试
测试从预处理到转录的完整流程，重点验证词级别时间戳
"""

import sys
import os
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_test_audio():
    """创建测试音频文件"""
    print("🎵 创建测试音频文件...")
    
    try:
        # 创建一个简单的测试音频（5秒，16kHz，单声道）
        duration = 5.0  # 秒
        sample_rate = 16000
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # 创建一个简单的正弦波信号（模拟语音）
        frequency = 440  # A4音符
        audio_data = np.sin(2 * np.pi * frequency * t) * 0.3
        
        # 添加一些变化来模拟语音模式
        envelope = np.exp(-t * 0.5)  # 衰减包络
        audio_data = audio_data * envelope
        
        # 保存为WAV文件
        import scipy.io.wavfile as wavfile
        test_audio_path = "test_audio.wav"
        wavfile.write(test_audio_path, sample_rate, audio_data.astype(np.float32))
        
        print(f"✅ 测试音频已创建: {test_audio_path}")
        print(f"📋 时长: {duration}秒, 采样率: {sample_rate}Hz")
        
        return test_audio_path
        
    except Exception as e:
        print(f"❌ 创建测试音频失败: {e}")
        return None

def test_transcriber_creation():
    """测试转录器创建"""
    print("\n🔍 测试转录器创建...")
    
    try:
        from audio_processor.transcribing import WhisperTranscriber
        
        # 创建转录器（不加载模型，只测试配置）
        transcriber = WhisperTranscriber("whisper", enable_preprocessing=False)
        
        print("✅ 转录器创建成功")
        print(f"📋 转录器类型: {transcriber.__class__.__name__}")
        
        # 检查配置
        model_name = transcriber.get_config_value("model.name", "unknown")
        print(f"📋 配置的模型: {model_name}")
        
        # 测试参数构建
        params = transcriber._build_transcription_params(
            language="en",
            temperature=0.0,
            word_timestamps=True,
            verbose=True
        )
        
        print(f"📋 构建的参数数量: {len(params)}")
        print("📋 关键参数:")
        for key in ["language", "temperature", "word_timestamps", "verbose"]:
            if key in params:
                print(f"   ✅ {key}: {params[key]}")
            else:
                print(f"   ❌ 缺少: {key}")
        
        return transcriber
        
    except Exception as e:
        print(f"❌ 转录器创建失败: {e}")
        return None

def test_model_loading():
    """测试模型加载"""
    print("\n🔍 测试模型加载...")
    
    try:
        # 直接测试原始Whisper库
        import whisper
        
        print("✅ whisper库导入成功")
        
        # 尝试加载最小的模型进行测试
        print("📋 加载tiny模型进行测试...")
        model = whisper.load_model("tiny")
        
        print("✅ 模型加载成功")
        print(f"📋 模型类型: {type(model)}")
        
        # 检查模型方法
        if hasattr(model, 'transcribe'):
            print("✅ transcribe方法存在")
        else:
            print("❌ transcribe方法不存在")
            return False
        
        return model
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        print("💡 提示: 请确保在正确的conda环境中运行")
        print("💡 提示: conda activate audio_processor_env")
        return None

def test_transcription_with_tiny_model(audio_path):
    """使用tiny模型测试转录"""
    print("\n🔍 测试转录功能（使用tiny模型）...")
    
    if not audio_path or not Path(audio_path).exists():
        print("❌ 测试音频文件不存在")
        return False
    
    try:
        import whisper
        
        # 加载tiny模型（最快，用于测试）
        print("📋 加载tiny模型...")
        model = whisper.load_model("tiny")
        
        # 执行转录
        print("📋 开始转录...")
        result = model.transcribe(
            audio_path,
            language="en",
            task="transcribe",
            verbose=True,
            word_timestamps=True,
            condition_on_previous_text=True,
            temperature=0.0
        )
        
        print("✅ 转录完成")
        
        # 检查结果结构
        if "text" in result:
            print(f"📋 转录文本: {result['text']}")
        else:
            print("❌ 结果中缺少text字段")
            return False
        
        if "segments" in result:
            print(f"📋 段落数量: {len(result['segments'])}")
            
            # 检查词级时间戳
            for i, segment in enumerate(result['segments']):
                print(f"📋 段落{i+1}: [{segment.get('start', 'N/A'):.2f} --> {segment.get('end', 'N/A'):.2f}] {segment.get('text', 'N/A')}")
                
                if 'words' in segment:
                    print(f"   ✅ 包含词级时间戳: {len(segment['words'])}个词")
                    for word in segment['words'][:3]:  # 显示前3个词
                        print(f"      - {word.get('word', 'N/A')}: [{word.get('start', 'N/A'):.2f} --> {word.get('end', 'N/A'):.2f}]")
                else:
                    print("   ⚠️  未找到词级时间戳")
        else:
            print("❌ 结果中缺少segments字段")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 转录测试失败: {e}")
        return False

def test_project_transcriber(audio_path):
    """测试项目转录器"""
    print("\n🔍 测试项目转录器...")
    
    if not audio_path or not Path(audio_path).exists():
        print("❌ 测试音频文件不存在")
        return False
    
    try:
        from audio_processor.transcribing import WhisperTranscriber
        
        # 创建转录器
        print("📋 创建项目转录器...")
        transcriber = WhisperTranscriber("whisper", enable_preprocessing=False)
        
        # 执行转录
        print("📋 开始转录...")
        result = transcriber.transcribe(
            audio_path,
            language="en",
            task="transcribe",
            verbose=True,
            word_timestamps=True,
            condition_on_previous_text=True,
            temperature=0.0
        )
        
        print("✅ 项目转录器转录完成")
        
        # 检查结果
        if "text" in result:
            print(f"📋 转录文本: {result['text']}")
        
        if "segments" in result:
            print(f"📋 段落数量: {len(result['segments'])}")
            
            # 检查词级时间戳
            for segment in result['segments']:
                if 'words' in segment:
                    print(f"   ✅ 词级时间戳正常工作")
                    break
            else:
                print("   ⚠️  词级时间戳可能未启用")
        
        return True
        
    except Exception as e:
        print(f"❌ 项目转录器测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    test_files = ["test_audio.wav"]
    for file in test_files:
        if Path(file).exists():
            os.remove(file)
            print(f"✅ 已删除: {file}")

def main():
    """主测试函数"""
    print("🚀 完整转录流程测试")
    print("=" * 60)
    
    # 测试步骤
    tests = []
    
    # 1. 创建测试音频
    audio_path = create_test_audio()
    if not audio_path:
        print("❌ 无法创建测试音频，跳过音频相关测试")
    
    # 2. 测试转录器创建
    transcriber = test_transcriber_creation()
    if transcriber:
        tests.append("转录器创建")
    
    # 3. 测试模型加载
    model = test_model_loading()
    if model:
        tests.append("模型加载")
    
    # 4. 测试转录功能
    if audio_path and model:
        if test_transcription_with_tiny_model(audio_path):
            tests.append("原始Whisper转录")
    
    # 5. 测试项目转录器
    if audio_path and transcriber:
        if test_project_transcriber(audio_path):
            tests.append("项目转录器")
    
    # 清理
    cleanup_test_files()
    
    # 总结
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {len(tests)}/5 项测试通过")
    print("📋 通过的测试:")
    for test in tests:
        print(f"   ✅ {test}")
    
    if len(tests) >= 3:
        print("\n🎉 核心功能测试通过！")
        print("✅ 原始OpenAI Whisper转录引擎替换成功")
        print("✅ 词级别时间戳功能正常")
        print("✅ 配置和参数构建正确")
        return True
    else:
        print("\n⚠️  部分测试失败，可能需要检查环境配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
