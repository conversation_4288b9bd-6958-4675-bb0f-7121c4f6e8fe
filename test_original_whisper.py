#!/usr/bin/env python3
"""
原始OpenAI Whisper转录器测试脚本
测试使用原始OpenAI Whisper库的转录器，重点关注词级别时间戳功能
"""

import os
import sys
import json
import time
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from audio_processor.transcribing.whisper_original import OriginalWhisperTranscriber


def create_short_test_audio():
    """创建一个短的测试音频文件（从长音频中截取前30秒）"""
    try:
        import librosa
        import soundfile as sf
        
        # 读取原始音频文件的前30秒
        audio_file = "data/samples/过量（二）.mp3"
        if not os.path.exists(audio_file):
            return None
            
        # 加载音频，只取前25秒（确保小于30秒限制）
        audio_data, sample_rate = librosa.load(audio_file, sr=16000, duration=25.0)
        
        # 保存短音频文件
        short_audio_path = "data/temp/short_test_audio.wav"
        os.makedirs("data/temp", exist_ok=True)
        sf.write(short_audio_path, audio_data, sample_rate)
        
        return short_audio_path
        
    except ImportError:
        print("⚠️  librosa未安装，无法创建短音频文件")
        return None
    except Exception as e:
        print(f"⚠️  创建短音频文件失败: {e}")
        return None


def test_original_whisper():
    """测试原始OpenAI Whisper转录器"""
    print("=" * 60)
    print("原始OpenAI Whisper转录器测试")
    print("=" * 60)
    
    try:
        # 创建短音频文件
        print("📁 准备测试音频文件...")
        short_audio = create_short_test_audio()
        
        if short_audio is None:
            print("⚠️  无法创建短音频文件，使用原始音频进行段级别测试")
            audio_file = "data/samples/过量（二）.mp3"
            test_word_level = False
        else:
            print(f"✅ 短音频文件已创建: {short_audio}")
            audio_file = short_audio
            test_word_level = True
        
        if not os.path.exists(audio_file):
            print(f"❌ 测试音频文件不存在: {audio_file}")
            return False
        
        # 初始化原始Whisper转录器
        transcriber = OriginalWhisperTranscriber(enable_preprocessing=False)
        
        # 1. 测试段级别时间戳
        print("\n" + "=" * 40)
        print("测试1: 段级别时间戳")
        print("=" * 40)
        
        result_segment = transcriber.transcribe(
            audio_file,
            return_timestamps=True,
            timestamp_granularity="segment"
        )
        
        print(f"✅ 段级别转录完成")
        print(f"📝 转录文本: {result_segment['text'][:100]}...")
        print(f"⏱️  处理时间: {result_segment['processing_time']:.2f}秒")
        
        segment_success = False
        if 'chunks' in result_segment:
            chunks = result_segment['chunks']
            print(f"📊 检测到 {len(chunks)} 个段级别时间戳")
            segment_success = True
            
            # 显示前几个段的时间戳
            print("\n前5个段的时间戳:")
            for i, chunk in enumerate(chunks[:5]):
                if 'timestamp' in chunk:
                    start_time, end_time = chunk['timestamp']
                    print(f"  {i+1:2d}. '{chunk['text'][:30]}...' [{start_time:.3f}s - {end_time:.3f}s]")
        else:
            print("❌ 未检测到段级别时间戳数据")
        
        # 保存段级别结果
        output_file_segment = "output/test_original_segment_timestamps.json"
        os.makedirs("output", exist_ok=True)
        with open(output_file_segment, 'w', encoding='utf-8') as f:
            json.dump(result_segment, f, ensure_ascii=False, indent=2)
        print(f"💾 段级别结果已保存到: {output_file_segment}")
        
        # 2. 测试词级别时间戳（仅在有短音频时）
        word_success = False
        if test_word_level:
            print("\n" + "=" * 40)
            print("测试2: 词级别时间戳")
            print("=" * 40)
            
            try:
                result_word = transcriber.transcribe(
                    audio_file,
                    return_timestamps=True,
                    timestamp_granularity="word"
                )
                
                print(f"✅ 词级别转录成功！")
                print(f"📝 转录文本: {result_word['text'][:100]}...")
                print(f"⏱️  处理时间: {result_word['processing_time']:.2f}秒")
                
                if 'chunks' in result_word:
                    chunks = result_word['chunks']
                    print(f"📊 检测到 {len(chunks)} 个词级别时间戳块")
                    
                    # 检查是否有词级别时间戳
                    words_found = False
                    for chunk in chunks:
                        if 'words' in chunk and len(chunk['words']) > 0:
                            words_found = True
                            break
                    
                    if words_found:
                        word_success = True
                        # 显示前几个词的时间戳
                        print("\n前10个词的时间戳:")
                        words_shown = 0
                        for chunk in chunks:
                            if 'words' in chunk:
                                for word in chunk['words'][:min(10 - words_shown, len(chunk['words']))]:
                                    if 'timestamp' in word:
                                        start_time, end_time = word['timestamp']
                                        print(f"  {words_shown+1:2d}. '{word['text']}' [{start_time:.3f}s - {end_time:.3f}s]")
                                        words_shown += 1
                                        if words_shown >= 10:
                                            break
                            if words_shown >= 10:
                                break
                    else:
                        print("❌ 未检测到词级别时间戳数据")
                else:
                    print("❌ 未检测到分段数据")
                
                # 保存词级别结果
                output_file_word = "output/test_original_word_timestamps.json"
                with open(output_file_word, 'w', encoding='utf-8') as f:
                    json.dump(result_word, f, ensure_ascii=False, indent=2)
                print(f"💾 词级别结果已保存到: {output_file_word}")
                
            except Exception as e:
                print(f"⚠️  词级别时间戳测试失败: {e}")
                word_success = False
        else:
            print("\n⚠️  跳过词级别时间戳测试（音频文件太长）")
        
        # 3. 测试预处理模式
        print("\n" + "=" * 40)
        print("测试3: 预处理模式时间戳")
        print("=" * 40)
        
        transcriber_prep = OriginalWhisperTranscriber(enable_preprocessing=True)
        
        try:
            result_prep = transcriber_prep.transcribe(
                audio_file,  # 使用同一个音频文件测试预处理
                return_timestamps=True,
                timestamp_granularity="segment"
            )
            
            print(f"✅ 预处理模式转录完成")
            print(f"📝 转录文本: {result_prep['text'][:100]}...")
            print(f"⏱️  总处理时间: {result_prep['processing_time']:.2f}秒")
            if 'preprocessing_time' in result_prep:
                print(f"⏱️  预处理时间: {result_prep['preprocessing_time']:.2f}秒")
            
            prep_success = False
            if 'segments' in result_prep:
                segments = result_prep['segments']
                print(f"📊 检测到 {len(segments)} 个预处理段")
                prep_success = True
                
                # 显示前几个段的信息
                print("\n前5个预处理段:")
                for i, segment in enumerate(segments[:5]):
                    print(f"  {i+1:2d}. [{segment['startTime']:.3f}s - {segment['endTime']:.3f}s] '{segment['sentence'][:50]}...'")
            else:
                print("❌ 未检测到预处理段数据")
            
            # 保存预处理结果
            output_file_prep = "output/test_original_preprocessing_timestamps.json"
            with open(output_file_prep, 'w', encoding='utf-8') as f:
                json.dump(result_prep, f, ensure_ascii=False, indent=2)
            print(f"💾 预处理结果已保存到: {output_file_prep}")
            
        except Exception as e:
            print(f"⚠️  预处理模式测试失败: {e}")
            prep_success = False
        
        # 4. 生成最终测试报告
        print("\n" + "=" * 40)
        print("最终测试报告")
        print("=" * 40)
        
        report = {
            "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_audio_file": audio_file,
            "original_audio_file": "data/samples/过量（二）.mp3",
            "tests": {
                "segment_timestamps": {
                    "success": segment_success,
                    "chunk_count": len(result_segment.get('chunks', [])),
                    "processing_time": result_segment['processing_time']
                },
                "word_timestamps": {
                    "tested": test_word_level,
                    "success": word_success,
                    "note": "词级别时间戳需要短音频文件（<30秒）"
                },
                "preprocessing_timestamps": {
                    "success": prep_success,
                    "note": "预处理模式使用段级别时间戳"
                }
            },
            "overall_success": segment_success,  # 至少段级别时间戳要工作
            "model_used": transcriber.model.name if hasattr(transcriber, 'model') and hasattr(transcriber.model, 'name') else "unknown"
        }
        
        # 保存最终测试报告
        report_file = "output/original_whisper_test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        success = report["overall_success"]
        
        print("\n" + "=" * 50)
        print("🎯 测试结论")
        print("=" * 50)
        
        if segment_success:
            print("✅ 段级别时间戳功能完全正常工作")
        else:
            print("❌ 段级别时间戳功能测试失败")
            
        if test_word_level:
            if word_success:
                print("✅ 词级别时间戳功能完全正常工作")
            else:
                print("❌ 词级别时间戳功能测试失败")
        
        if prep_success:
            print("✅ 预处理模式完全正常工作")
        else:
            print("❌ 预处理模式测试失败")
        
        print(f"\n使用的Whisper模型: {report['model_used']}")
        print(f"测试时间: {report['test_timestamp']}")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始测试原始OpenAI Whisper转录器...")
    success = test_original_whisper()
    
    if success:
        print("\n✅ 原始OpenAI Whisper转录器测试成功！")
        return 0
    else:
        print("\n❌ 原始OpenAI Whisper转录器测试失败！")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 