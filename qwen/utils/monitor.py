#!/usr/bin/env python3
"""
系统监控模块
监控内存和GPU使用情况，支持Whisper和Qwen模型的专门监控
"""

import time
import threading
import subprocess
import gc
from typing import Dict, Any, Optional
from pathlib import Path
from enum import Enum

import psutil
import torch
from loguru import logger

try:
    import gpustat
    GPUSTAT_AVAILABLE = True
except ImportError:
    GPUSTAT_AVAILABLE = False
    logger.warning("gpustat 不可用，将使用基础GPU监控")


class ModelType(Enum):
    """模型类型枚举"""
    WHISPER = "whisper"
    QWEN = "qwen"


class ModelStatus(Enum):
    """模型状态枚举"""
    UNLOADED = "unloaded"
    LOADING = "loading"
    LOADED = "loaded"
    PROCESSING = "processing"
    UNLOADING = "unloading"


class SystemMonitor:
    """系统资源监控器"""

    def __init__(self, config: Dict[str, Any]):
        """初始化监控器

        Args:
            config: 配置字典
        """
        self.config = config
        self.monitoring_config = config.get("monitoring", {})
        self.enable_memory_monitoring = self.monitoring_config.get("enable_memory_monitoring", True)
        self.enable_gpu_monitoring = self.monitoring_config.get("enable_gpu_monitoring", True)
        self.log_interval = self.monitoring_config.get("log_interval", 10)

        self.monitoring = False
        self.monitor_thread = None

        # 初始状态
        self.initial_memory = None
        self.initial_gpu_memory = None

        # 模型监控状态
        self.model_states: Dict[ModelType, ModelStatus] = {
            ModelType.WHISPER: ModelStatus.UNLOADED,
            ModelType.QWEN: ModelStatus.UNLOADED
        }
        self.model_memory_usage: Dict[ModelType, Dict[str, float]] = {
            ModelType.WHISPER: {},
            ModelType.QWEN: {}
        }

        # 设置专门的监控日志记录器
        self._setup_monitor_logger()

    def _setup_monitor_logger(self):
        """设置专门的监控日志记录器"""
        try:
            # 创建监控专用的日志记录器
            self.monitor_logger = logger.bind(monitor=True)

            # 添加监控日志文件处理器
            logs_dir = self.config.get("paths", {}).get("logs_dir", "logs")
            monitor_log_file = f"{logs_dir}/system_monitor.log"

            # 确保日志目录存在
            Path(logs_dir).mkdir(exist_ok=True)

            # 添加监控日志文件
            logger.add(
                monitor_log_file,
                format="{time:YYYY-MM-DD HH:mm:ss} | {level} | MONITOR | {message}",
                level="INFO",
                rotation="10 MB",
                retention="7 days",
                filter=lambda record: record["extra"].get("monitor", False)
            )

        except Exception as e:
            logger.warning(f"设置监控日志记录器失败: {e}")
            self.monitor_logger = logger

    def update_model_status(self, model_type: ModelType, status: ModelStatus, memory_info: Optional[Dict[str, float]] = None):
        """更新模型状态

        Args:
            model_type: 模型类型
            status: 模型状态
            memory_info: 内存使用信息
        """
        old_status = self.model_states.get(model_type, ModelStatus.UNLOADED)
        self.model_states[model_type] = status

        if memory_info:
            self.model_memory_usage[model_type] = memory_info

        # 记录状态变化
        self.monitor_logger.info(f"模型状态变化: {model_type.value} {old_status.value} -> {status.value}")

        if memory_info:
            gpu_memory = memory_info.get("gpu_memory_allocated", 0)
            system_memory = memory_info.get("system_memory_used", 0)
            self.monitor_logger.info(f"模型内存使用: {model_type.value} GPU={gpu_memory:.2f}GB, 系统={system_memory:.2f}GB")

    def get_model_status(self, model_type: ModelType) -> ModelStatus:
        """获取模型状态

        Args:
            model_type: 模型类型

        Returns:
            模型状态
        """
        return self.model_states.get(model_type, ModelStatus.UNLOADED)

    def get_memory_info(self) -> Dict[str, float]:
        """获取内存信息

        Returns:
            内存信息字典 (GB)
        """
        memory = psutil.virtual_memory()
        return {
            "total": memory.total / 1024**3,
            "available": memory.available / 1024**3,
            "used": memory.used / 1024**3,
            "percent": memory.percent
        }

    def get_gpu_info(self) -> Dict[str, Any]:
        """获取GPU信息

        Returns:
            GPU信息字典
        """
        if not torch.cuda.is_available():
            return {"available": False}

        gpu_info = {"available": True, "devices": []}

        try:
            for i in range(torch.cuda.device_count()):
                device_info = {
                    "id": i,
                    "name": torch.cuda.get_device_name(i),
                }

                # 获取内存信息
                if torch.cuda.is_available():
                    memory_allocated = torch.cuda.memory_allocated(i) / 1024**3
                    memory_reserved = torch.cuda.memory_reserved(i) / 1024**3
                    memory_total = torch.cuda.get_device_properties(i).total_memory / 1024**3

                    device_info.update({
                        "memory_allocated": memory_allocated,
                        "memory_reserved": memory_reserved,
                        "memory_total": memory_total,
                        "memory_free": memory_total - memory_reserved,
                        "memory_percent": (memory_reserved / memory_total) * 100
                    })

                # 尝试获取更详细的GPU信息
                if GPUSTAT_AVAILABLE:
                    try:
                        gpu_stats = gpustat.new_query()
                        if i < len(gpu_stats.gpus):
                            gpu = gpu_stats.gpus[i]
                            device_info.update({
                                "temperature": gpu.temperature,
                                "utilization": gpu.utilization,
                                "power_draw": getattr(gpu, 'power_draw', None),
                                "power_limit": getattr(gpu, 'power_limit', None)
                            })
                    except Exception as e:
                        logger.debug(f"获取GPU详细信息失败: {e}")

                gpu_info["devices"].append(device_info)

        except Exception as e:
            logger.error(f"获取GPU信息失败: {e}")
            gpu_info["error"] = str(e)

        return gpu_info

    def log_system_status(self):
        """记录系统状态"""
        status_lines = ["=== 系统状态 ==="]

        # 内存信息
        if self.enable_memory_monitoring:
            memory_info = self.get_memory_info()
            status_lines.extend([
                f"内存使用: {memory_info['used']:.1f}GB / {memory_info['total']:.1f}GB ({memory_info['percent']:.1f}%)",
                f"可用内存: {memory_info['available']:.1f}GB"
            ])

        # GPU信息
        if self.enable_gpu_monitoring:
            gpu_info = self.get_gpu_info()
            if gpu_info["available"]:
                for device in gpu_info["devices"]:
                    gpu_line = f"GPU {device['id']} ({device['name']}): "
                    gpu_line += f"显存 {device['memory_allocated']:.1f}GB / {device['memory_total']:.1f}GB "
                    gpu_line += f"({device['memory_percent']:.1f}%)"

                    if "utilization" in device:
                        gpu_line += f", 利用率 {device['utilization']}%"
                    if "temperature" in device:
                        gpu_line += f", 温度 {device['temperature']}°C"

                    status_lines.append(gpu_line)
            else:
                status_lines.append("GPU: 不可用")

        # 模型状态信息
        status_lines.append("=== 模型状态 ===")
        for model_type, status in self.model_states.items():
            model_line = f"{model_type.value.upper()}: {status.value}"

            # 添加内存使用信息
            if model_type in self.model_memory_usage and self.model_memory_usage[model_type]:
                memory_info = self.model_memory_usage[model_type]
                gpu_mem = memory_info.get("gpu_memory_allocated", 0)
                sys_mem = memory_info.get("system_memory_used", 0)
                if gpu_mem > 0 or sys_mem > 0:
                    model_line += f" (GPU: {gpu_mem:.2f}GB, 系统: {sys_mem:.2f}GB)"

            status_lines.append(model_line)

        # 记录状态到监控日志
        for line in status_lines:
            self.monitor_logger.info(line)

    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                self.log_system_status()
                time.sleep(self.log_interval)
            except Exception as e:
                logger.error(f"监控过程中出错: {e}")
                time.sleep(self.log_interval)

    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            logger.warning("监控已经在运行")
            return

        logger.info("开始系统监控")

        # 记录初始状态
        if self.enable_memory_monitoring:
            self.initial_memory = self.get_memory_info()

        if self.enable_gpu_monitoring:
            self.initial_gpu_memory = self.get_gpu_info()

        # 记录初始状态
        self.log_system_status()

        # 启动监控线程
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()

    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring:
            return

        logger.info("停止系统监控")
        self.monitoring = False

        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)

        # 记录最终状态和变化
        self._log_final_status()

    def _log_final_status(self):
        """记录最终状态和资源使用变化"""
        logger.info("=== 最终系统状态 ===")

        # 当前状态
        if self.enable_memory_monitoring:
            current_memory = self.get_memory_info()
            logger.info(f"最终内存使用: {current_memory['used']:.1f}GB / {current_memory['total']:.1f}GB ({current_memory['percent']:.1f}%)")

            # 计算内存变化
            if self.initial_memory:
                memory_change = current_memory['used'] - self.initial_memory['used']
                if abs(memory_change) > 0.1:  # 变化超过100MB才记录
                    change_sign = "+" if memory_change > 0 else ""
                    logger.info(f"内存使用变化: {change_sign}{memory_change:.1f}GB")

        if self.enable_gpu_monitoring:
            current_gpu = self.get_gpu_info()
            if current_gpu["available"]:
                for i, device in enumerate(current_gpu["devices"]):
                    logger.info(f"GPU {device['id']} 最终显存: {device['memory_allocated']:.1f}GB / {device['memory_total']:.1f}GB ({device['memory_percent']:.1f}%)")

                    # 计算GPU内存变化
                    if (self.initial_gpu_memory and
                        self.initial_gpu_memory["available"] and
                        i < len(self.initial_gpu_memory["devices"])):

                        initial_device = self.initial_gpu_memory["devices"][i]
                        memory_change = device['memory_allocated'] - initial_device['memory_allocated']

                        if abs(memory_change) > 0.1:  # 变化超过100MB才记录
                            change_sign = "+" if memory_change > 0 else ""
                            logger.info(f"GPU {device['id']} 显存变化: {change_sign}{memory_change:.1f}GB")

    def get_current_status(self) -> Dict[str, Any]:
        """获取当前系统状态

        Returns:
            当前系统状态字典
        """
        status: Dict[str, Any] = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

        if self.enable_memory_monitoring:
            status["memory"] = self.get_memory_info()

        if self.enable_gpu_monitoring:
            status["gpu"] = self.get_gpu_info()

        # 添加模型状态信息
        status["models"] = {}
        for model_type, model_status in self.model_states.items():
            model_info = {
                "status": model_status.value,
                "memory_usage": self.model_memory_usage.get(model_type, {})
            }
            status["models"][model_type.value] = model_info

        return status

    def get_real_gpu_memory(self, device_id: int = 0) -> float:
        """获取真实的GPU显存使用情况（MB）

        Args:
            device_id: GPU设备ID

        Returns:
            GPU显存使用量（MB）
        """
        try:
            result = subprocess.run(
                ['nvidia-smi', '--query-gpu=memory.used', '--format=csv,noheader,nounits'],
                capture_output=True, text=True, timeout=5
            )
            if result.returncode == 0:
                # 获取指定GPU的显存使用情况
                memory_lines = result.stdout.strip().split('\n')
                if device_id < len(memory_lines):
                    memory_used = int(memory_lines[device_id])
                    return memory_used
                else:
                    logger.warning(f"GPU {device_id} 不存在，使用 PyTorch 显存检测")
                    if torch.cuda.is_available() and device_id < torch.cuda.device_count():
                        return torch.cuda.memory_allocated(device_id) / 1024**2  # MB
                    return 0.0
            else:
                logger.warning("nvidia-smi 命令执行失败，使用 PyTorch 显存检测")
                if torch.cuda.is_available() and device_id < torch.cuda.device_count():
                    return torch.cuda.memory_allocated(device_id) / 1024**2  # MB
                return 0.0
        except Exception as e:
            logger.warning(f"获取真实GPU显存失败: {e}，使用 PyTorch 显存检测")
            if torch.cuda.is_available() and device_id < torch.cuda.device_count():
                return torch.cuda.memory_allocated(device_id) / 1024**2  # MB
            return 0.0

    def clear_gpu_cache(self, device_id: Optional[int] = None):
        """清理GPU缓存

        Args:
            device_id: 指定GPU设备ID，None表示清理所有GPU
        """
        if torch.cuda.is_available():
            try:
                if device_id is not None:
                    with torch.cuda.device(device_id):
                        torch.cuda.empty_cache()
                    logger.info(f"已清理GPU {device_id} 缓存")
                else:
                    torch.cuda.empty_cache()
                    logger.info("已清理所有GPU缓存")

                # 记录清理后的状态
                if self.enable_gpu_monitoring:
                    gpu_info = self.get_gpu_info()
                    for device in gpu_info["devices"]:
                        logger.info(f"GPU {device['id']} 清理后显存: {device['memory_allocated']:.1f}GB / {device['memory_total']:.1f}GB")
            except Exception as e:
                logger.error(f"清理GPU缓存失败: {e}")
        else:
            logger.warning("GPU不可用，无法清理缓存")

    def clear_gpu_memory(self, device_id: int = 0) -> Dict[str, Any]:
        """清理GPU显存

        Args:
            device_id: GPU设备ID

        Returns:
            清理结果信息
        """
        try:
            # 获取清理前的显存使用情况（真实显存）
            memory_before_mb = self.get_real_gpu_memory(device_id)
            memory_before_gb = memory_before_mb / 1024

            logger.info(f"开始清理GPU {device_id} 显存...")

            # 清理PyTorch缓存
            if torch.cuda.is_available():
                if device_id < torch.cuda.device_count():
                    with torch.cuda.device(device_id):
                        torch.cuda.empty_cache()
                        torch.cuda.synchronize()
                else:
                    logger.warning(f"GPU {device_id} 不存在")
                    return {
                        "success": False,
                        "error": f"GPU {device_id} 不存在",
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    }

            # 强制垃圾回收
            gc.collect()

            # 等待一下让清理生效
            time.sleep(0.5)

            # 获取清理后的显存使用情况（真实显存）
            memory_after_mb = self.get_real_gpu_memory(device_id)
            memory_after_gb = memory_after_mb / 1024

            memory_freed_gb = memory_before_gb - memory_after_gb

            result = {
                "success": True,
                "device_id": device_id,
                "memory_before_gb": round(memory_before_gb, 2),
                "memory_after_gb": round(memory_after_gb, 2),
                "memory_freed_gb": round(memory_freed_gb, 2),
                "real_memory_detection": True,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }

            logger.info(f"GPU {device_id} 显存清理完成，释放了 {memory_freed_gb:.2f} GB")
            return result

        except Exception as e:
            logger.error(f"GPU {device_id} 显存清理失败: {e}")
            return {
                "success": False,
                "device_id": device_id,
                "error": str(e),
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }

    def get_preferred_device(self) -> str:
        """获取首选设备

        Returns:
            设备字符串 ("cuda" 或 "cpu")
        """
        gpu_info = self.get_gpu_info()
        return "cuda" if gpu_info["available"] else "cpu"

    def get_device_summary(self) -> Dict[str, Any]:
        """获取设备摘要信息

        Returns:
            设备摘要信息字典
        """
        gpu_info = self.get_gpu_info()

        summary = {
            "cuda_available": gpu_info["available"],
            "gpu_count": len(gpu_info.get("devices", [])) if gpu_info["available"] else 0,
            "gpu_names": [],
            "total_vram": 0.0
        }

        if gpu_info["available"]:
            for device in gpu_info["devices"]:
                summary["gpu_names"].append(device["name"])
                summary["total_vram"] += device.get("memory_total", 0)

        return summary

    def log_device_summary(self):
        """记录设备信息摘要"""
        summary = self.get_device_summary()

        if summary["cuda_available"]:
            self.monitor_logger.info(f"GPU加速可用: {summary['gpu_count']} 个GPU, 总显存: {summary['total_vram']:.1f} GB")
            gpu_info = self.get_gpu_info()
            for device in gpu_info["devices"]:
                self.monitor_logger.info(f"  GPU {device['id']}: {device['name']} ({device['memory_total']:.1f} GB)")
        else:
            self.monitor_logger.warning("GPU加速不可用，将使用CPU模式")


class ModelMonitorMixin:
    """模型监控混入类，为模型处理器提供监控功能"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._monitor_instance = None
        self._model_type = None

    def _setup_model_monitoring(self, monitor: SystemMonitor, model_type: ModelType):
        """设置模型监控

        Args:
            monitor: 系统监控器实例
            model_type: 模型类型
        """
        self._monitor_instance = monitor
        self._model_type = model_type

    def _update_model_status(self, status: ModelStatus, memory_info: Optional[Dict[str, float]] = None):
        """更新模型状态

        Args:
            status: 模型状态
            memory_info: 内存使用信息
        """
        if self._monitor_instance and self._model_type:
            self._monitor_instance.update_model_status(self._model_type, status, memory_info)

    def _get_current_memory_info(self) -> Dict[str, float]:
        """获取当前内存使用信息

        Returns:
            内存使用信息字典
        """
        memory_info = {}

        # 系统内存
        system_memory = psutil.virtual_memory()
        memory_info["system_memory_used"] = system_memory.used / 1024**3

        # GPU内存
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.memory_allocated() / 1024**3
            memory_info["gpu_memory_allocated"] = gpu_memory

        return memory_info


def main():
    """测试监控功能"""
    config = {
        "monitoring": {
            "enable_memory_monitoring": True,
            "enable_gpu_monitoring": True,
            "log_interval": 5
        }
    }

    monitor = SystemMonitor(config)

    print("开始监控测试...")
    monitor.start_monitoring()

    try:
        # 模拟一些工作
        time.sleep(15)
    except KeyboardInterrupt:
        print("\n用户中断")
    finally:
        monitor.stop_monitoring()
        print("监控测试完成")


if __name__ == "__main__":
    main()
