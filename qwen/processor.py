"""
Qwen 处理器模块
基于 llama-cpp-python 的 Qwen Q5_0 模型集成
"""

import time
import gc
from pathlib import Path
from typing import Dict, Any, List, Optional

from loguru import logger

try:
    from llama_cpp import <PERSON>lama
    import torch
except ImportError:
    logger.error("llama-cpp-python 或 torch 未安装。请运行: pip install llama-cpp-python torch")
    raise

from core.base import BaseProcessor
from utils.monitor import SystemMonitor, ModelMonitorMixin, ModelType, ModelStatus
from utils.device import get_device_manager


class QwenProcessor(BaseProcessor, ModelMonitorMixin):
    """Qwen 文本生成处理器"""

    def __init__(self, config_name: str = "qwen"):
        """初始化Qwen处理器

        Args:
            config_name: 配置名称
        """
        super().__init__(config_name)

        # 初始化监控器
        self.monitor = SystemMonitor(self.config)

        # 设置模型监控
        self._setup_model_monitoring(self.monitor, ModelType.QWEN)
        self._update_model_status(ModelStatus.LOADING)

        # 初始化设备管理器
        self.device_manager = get_device_manager(self.module_logger)
        self.device_info = self.device_manager.get_device_info(detailed=True)
        self.device_manager.log_device_summary()

        # 初始化模型
        self.model = None
        self._load_model()

        # 确保模型已初始化
        if self.model is None:
            self._update_model_status(ModelStatus.UNLOADED)
            raise RuntimeError("Qwen模型初始化失败")

        # 更新模型状态为已加载
        memory_info = self._get_current_memory_info()
        self._update_model_status(ModelStatus.LOADED, memory_info)



    def _load_model(self):
        """加载Qwen模型"""
        model_path = self.get_config_value("model.path", "audio_processor/models/qwen/Qwen3-32B-Q5_0.gguf")

        # 确保模型文件存在
        if not Path(model_path).exists():
            logger.error(f"模型文件不存在: {model_path}")
            raise FileNotFoundError(f"模型文件不存在: {model_path}")

        logger.info(f"正在加载Qwen模型: {model_path}")

        try:
            # 获取模型参数
            n_gpu_layers = self.get_config_value("model.n_gpu_layers", -1)  # -1表示全部使用GPU
            n_ctx = self.get_config_value("model.n_ctx", 32768)
            n_batch = self.get_config_value("model.n_batch", 512)
            n_threads = self.get_config_value("model.n_threads", None)

            # 如果有GPU，使用GPU加速
            if self.device_info["cuda_available"]:
                logger.info(f"使用GPU加速，GPU层数: {n_gpu_layers}")
            else:
                n_gpu_layers = 0
                logger.warning("未检测到CUDA，使用CPU模式")

            # 加载模型
            self.model = Llama(
                model_path=str(model_path),
                n_gpu_layers=n_gpu_layers,
                n_ctx=n_ctx,
                n_batch=n_batch,
                n_threads=n_threads,
                verbose=False,  # 减少输出
                use_mmap=True,  # 使用内存映射
                use_mlock=True,  # 锁定内存
            )

            logger.info("Qwen模型加载完成")

        except Exception as e:
            logger.error(f"Qwen模型加载失败: {e}")
            raise

    def generate_text(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        top_k: Optional[int] = None,
        min_p: Optional[float] = None,
        repeat_penalty: Optional[float] = None,
        presence_penalty: Optional[float] = None,
        stop: Optional[List[str]] = None,
        task_type: Optional[str] = None,
        enable_thinking: Optional[bool] = None
    ) -> Dict[str, Any]:
        """生成文本

        Args:
            prompt: 输入提示
            system_prompt: 系统提示词
            max_tokens: 最大生成token数
            temperature: 温度参数
            top_p: top_p参数
            top_k: top_k参数
            min_p: min_p参数
            repeat_penalty: 重复惩罚
            presence_penalty: 存在惩罚
            stop: 停止词列表
            task_type: 任务类型 ('math', 'programming', 'multiple_choice')
            enable_thinking: 是否启用思维模式
            **kwargs: 其他参数

        Returns:
            生成结果字典
        """
        if not prompt.strip():
            raise ValueError("输入提示不能为空")

        logger.info(f"开始生成文本，提示长度: {len(prompt)}")

        # 开始监控
        self.monitor.start_monitoring()

        # 更新模型状态为处理中
        self._update_model_status(ModelStatus.PROCESSING)

        try:
            start_time = time.time()

            # 检测思维模式
            is_thinking_mode = self._detect_thinking_mode(prompt, enable_thinking)

            # 应用官方最佳实践参数
            generation_params = self._get_optimal_params(
                task_type=task_type,
                enable_thinking=is_thinking_mode,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                min_p=min_p,
                repeat_penalty=repeat_penalty,
                presence_penalty=presence_penalty,
                stop=stop
            )

            # 构建最终提示（包含系统提示词和思考模式标签）
            final_prompt = self._build_prompt_with_system(prompt, system_prompt, task_type, is_thinking_mode)

            logger.info(f"思维模式: {is_thinking_mode}")
            logger.info(f"任务类型: {task_type or 'general'}")
            logger.info(f"生成参数: {generation_params}")

            # 执行生成
            logger.info("正在生成文本...")
            response = self.model.create_completion(  # type: ignore
                prompt=final_prompt,
                **generation_params
            )

            end_time = time.time()
            processing_time = end_time - start_time

            # 提取生成的文本
            generated_text = response["choices"][0]["text"]  # type: ignore

            # 清理思维内容（如果需要）
            if is_thinking_mode:
                generated_text = self._clean_thinking_content(generated_text)

            # 构建结果
            result = {
                "generated_text": generated_text,
                "prompt": prompt,
                "system_prompt": system_prompt,
                "final_prompt": final_prompt,
                "processing_time": processing_time,
                "model_path": self.get_config_value("model.path"),
                "generation_params": generation_params,
                "usage": response.get("usage", {}),  # type: ignore
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "thinking_mode": is_thinking_mode,
                "task_type": task_type
            }

            logger.info(f"文本生成完成，耗时: {processing_time:.2f}秒")
            logger.info(f"生成文本长度: {len(generated_text)}")

            return result

        except Exception as e:
            logger.error(f"文本生成失败: {e}")
            # 更新模型状态为已加载（处理失败）
            memory_info = self._get_current_memory_info()
            self._update_model_status(ModelStatus.LOADED, memory_info)
            raise
        finally:
            # 停止监控
            self.monitor.stop_monitoring()

            # 恢复模型状态为已加载
            memory_info = self._get_current_memory_info()
            self._update_model_status(ModelStatus.LOADED, memory_info)

            # 根据配置决定是否自动清理显存
            auto_clear_memory = self.get_config_value("performance.auto_clear_memory", False)
            if auto_clear_memory:
                # 使用Monitor的GPU内存清理功能
                device_id = 0  # 默认使用第一个GPU
                self.monitor.clear_gpu_memory(device_id)

    def _build_prompt_with_system(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        task_type: Optional[str] = None,
        enable_thinking: Optional[bool] = None
    ) -> str:
        """构建包含系统提示词和思考模式标签的完整提示

        Args:
            prompt: 用户输入提示
            system_prompt: 系统提示词
            task_type: 任务类型
            enable_thinking: 是否启用思考模式

        Returns:
            格式化的完整提示字符串
        """
        prompt_parts = []

        # 获取任务特定的系统提示词
        if task_type and not system_prompt:
            task_config = self.get_config_value(f"task_configs.{task_type}", {})
            system_prompt = task_config.get("system_prompt")

        # 添加系统提示词
        if system_prompt:
            prompt_parts.append(f"<|im_start|>system\n{system_prompt}<|im_end|>")

        # 处理用户提示，根据思考模式添加相应标签
        user_prompt = self._add_thinking_tags(prompt, enable_thinking)
        prompt_parts.append(f"<|im_start|>user\n{user_prompt}<|im_end|>")

        # 添加助手开始标记
        prompt_parts.append("<|im_start|>assistant\n")

        return "\n".join(prompt_parts)

    def _add_thinking_tags(self, prompt: str, enable_thinking: Optional[bool]) -> str:
        """根据思考模式设置，在提示词中添加相应的官方标签

        Args:
            prompt: 原始用户提示
            enable_thinking: 是否启用思考模式

        Returns:
            添加了思考标签的提示词
        """
        # 如果提示词中已经包含思考标签，直接返回
        if "/think" in prompt or "/no_think" in prompt:
            return prompt

        # 根据 enable_thinking 参数添加相应标签
        if enable_thinking is True:
            # 启用思考模式：添加 /think 标签
            return f"{prompt} /think"
        elif enable_thinking is False:
            # 禁用思考模式：添加 /no_think 标签
            return f"{prompt} /no_think"
        else:
            # 未明确指定，保持原样（让模型根据内容自动判断）
            return prompt

    def _detect_thinking_mode(self, prompt: str, enable_thinking: Optional[bool]) -> bool:
        """检测是否应该启用思维模式

        Args:
            prompt: 输入提示
            enable_thinking: 显式指定的思维模式

        Returns:
            是否启用思维模式
        """
        if enable_thinking is not None:
            return enable_thinking

        # 检查提示中的思维模式指令
        if "/think" in prompt:
            return True
        elif "/no_think" in prompt:
            return False

        # 默认根据提示复杂度判断
        # 复杂的数学、编程、推理问题默认启用思维模式
        thinking_keywords = [
            "解释", "分析", "推理", "证明", "计算", "编程", "算法",
            "explain", "analyze", "reason", "prove", "calculate", "program", "algorithm"
        ]

        return any(keyword in prompt.lower() for keyword in thinking_keywords)

    def _get_optimal_params(
        self,
        task_type: Optional[str] = None,
        enable_thinking: bool = False,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        top_k: Optional[int] = None,
        min_p: Optional[float] = None,
        repeat_penalty: Optional[float] = None,
        presence_penalty: Optional[float] = None,
        stop: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """获取优化的生成参数，应用官方最佳实践

        Args:
            task_type: 任务类型
            enable_thinking: 是否启用思维模式
            其他参数: 覆盖默认值的参数

        Returns:
            优化的生成参数字典
        """
        # 根据任务类型获取特定配置
        if task_type and task_type in ["math", "programming", "multiple_choice"]:
            task_config = self.get_config_value(f"task_configs.{task_type}", {})
            base_params = task_config.copy()
        else:
            # 根据思维模式选择基础参数
            if enable_thinking:
                base_params = self.get_config_value("thinking_mode", {})
            else:
                base_params = self.get_config_value("non_thinking_mode", {})

        # 应用默认值
        params = {
            "max_tokens": max_tokens or base_params.get("max_tokens") or self.get_config_value("generation.max_tokens", 32768),
            "temperature": temperature if temperature is not None else base_params.get("temperature", 0.6),
            "top_p": top_p if top_p is not None else base_params.get("top_p", 0.95),
            "top_k": top_k if top_k is not None else base_params.get("top_k", 20),
            "min_p": min_p if min_p is not None else base_params.get("min_p", 0),
            "repeat_penalty": repeat_penalty if repeat_penalty is not None else base_params.get("repeat_penalty", 1.0),
            "presence_penalty": presence_penalty if presence_penalty is not None else base_params.get("presence_penalty", 1.5),
            "stop": stop or base_params.get("stop") or self.get_config_value("generation.stop", ["<|im_start|>", "<|im_end|>"])
        }

        # 确保不使用贪婪解码（官方建议）
        if params["temperature"] == 0:
            params["temperature"] = 0.1
            logger.warning("避免贪婪解码，将temperature从0调整为0.1")

        return params

    def _clean_thinking_content(self, text: str) -> str:
        """清理思维内容，只保留最终输出

        Args:
            text: 原始生成文本

        Returns:
            清理后的文本
        """
        # 移除 <think> 标签内的内容
        import re

        # 首先处理完整的 <think>...</think> 标签对
        think_pattern = r'<think>.*?</think>'
        cleaned_text = re.sub(think_pattern, '', text, flags=re.DOTALL)

        # 处理未闭合的 <think> 标签，移除从 <think> 开始的所有内容
        if '<think>' in cleaned_text:
            cleaned_text = cleaned_text.split('<think>')[0]

        # 清理多余的空行和空白字符
        cleaned_text = re.sub(r'\n\s*\n', '\n\n', cleaned_text.strip())

        # 如果清理后的文本为空或只有空白字符，返回一个默认回复
        if not cleaned_text.strip():
            return "我已经完成了思考，但最终回复被过滤了。请重新提问。"

        return cleaned_text

    def process(self, input_data: str, **kwargs) -> Dict[str, Any]:
        """处理方法 - 实现BaseProcessor的抽象方法

        Args:
            input_data: 输入文本
            **kwargs: 其他参数

        Returns:
            处理结果
        """
        return self.generate_text(input_data, **kwargs)



    def clear_gpu_memory(self) -> Dict[str, Any]:
        """清理GPU显存

        Returns:
            清理结果信息
        """
        # 使用Monitor的GPU内存清理功能
        device_id = 0  # 默认使用第一个GPU
        return self.monitor.clear_gpu_memory(device_id)

    def unload_model(self) -> Dict[str, Any]:
        """卸载模型，完全释放显存

        Returns:
            卸载结果信息
        """
        try:
            logger.info("开始卸载模型...")

            # 更新模型状态为卸载中
            self._update_model_status(ModelStatus.UNLOADING)

            # 获取卸载前的显存使用情况（真实显存）
            device_id = 0  # 默认使用第一个GPU
            memory_before_mb = self.monitor.get_real_gpu_memory(device_id)
            memory_before_gb = memory_before_mb / 1024

            # 删除模型实例
            if self.model is not None:
                del self.model
                self.model = None
                logger.info("模型实例已删除")

            # 清理显存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()

            # 强制垃圾回收
            gc.collect()

            # 等待一下让卸载生效
            time.sleep(1.0)

            # 获取卸载后的显存使用情况（真实显存）
            memory_after_mb = self.monitor.get_real_gpu_memory(device_id)
            memory_after_gb = memory_after_mb / 1024

            memory_freed_gb = memory_before_gb - memory_after_gb

            result = {
                "success": True,
                "model_unloaded": True,
                "memory_before_gb": round(memory_before_gb, 2),
                "memory_after_gb": round(memory_after_gb, 2),
                "memory_freed_gb": round(memory_freed_gb, 2),
                "real_memory_detection": True,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }

            # 更新模型状态为已卸载
            self._update_model_status(ModelStatus.UNLOADED)

            logger.info(f"模型卸载完成，释放了 {memory_freed_gb:.2f} GB 显存")
            return result

        except Exception as e:
            logger.error(f"模型卸载失败: {e}")
            # 卸载失败，恢复为已加载状态
            memory_info = self._get_current_memory_info()
            self._update_model_status(ModelStatus.LOADED, memory_info)
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }

    def reload_model(self) -> Dict[str, Any]:
        """重新加载模型

        Returns:
            重新加载结果信息
        """
        try:
            logger.info("开始重新加载模型...")

            # 先卸载现有模型
            unload_result = self.unload_model()

            # 更新状态为加载中
            self._update_model_status(ModelStatus.LOADING)

            # 重新加载模型
            self._load_model()

            # 更新状态为已加载
            memory_info = self._get_current_memory_info()
            self._update_model_status(ModelStatus.LOADED, memory_info)

            result = {
                "success": True,
                "model_reloaded": True,
                "unload_info": unload_result,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }

            logger.info("模型重新加载完成")
            return result

        except Exception as e:
            logger.error(f"模型重新加载失败: {e}")
            # 重新加载失败，更新状态为未加载
            self._update_model_status(ModelStatus.UNLOADED)
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息

        Returns:
            模型信息字典
        """
        if self.model is None:
            return {"error": "模型未加载"}

        # 获取当前显存使用情况（真实显存）
        memory_info = {}
        try:
            # 真实显存使用情况
            device_id = 0  # 默认使用第一个GPU
            real_memory_mb = self.monitor.get_real_gpu_memory(device_id)
            real_memory_gb = real_memory_mb / 1024

            memory_info = {
                "real_gpu_memory_used_gb": round(real_memory_gb, 2),
                "real_gpu_memory_used_mb": round(real_memory_mb, 2)
            }

            # 如果 PyTorch 可用，也显示 PyTorch 检测到的显存
            if torch.cuda.is_available():
                pytorch_allocated = torch.cuda.memory_allocated() / 1024**3
                pytorch_reserved = torch.cuda.memory_reserved() / 1024**3
                memory_info.update({
                    "pytorch_memory_allocated_gb": round(pytorch_allocated, 2),
                    "pytorch_memory_reserved_gb": round(pytorch_reserved, 2)
                })
        except Exception as e:
            logger.warning(f"获取显存信息失败: {e}")
            memory_info = {"error": str(e)}

        return {
            "model_path": self.get_config_value("model.path"),
            "context_length": self.get_config_value("model.n_ctx", 32768),
            "gpu_layers": self.get_config_value("model.n_gpu_layers", -1),
            "device_info": self.device_info,
            "memory_info": memory_info,
            "auto_clear_memory": self.get_config_value("performance.auto_clear_memory", False),
            "loaded": True
        }
