"""
API 路由定义
"""

import time
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from loguru import logger

from .models import (
    GenerateTextRequest,
    GenerateTextResponse,
    ModelInfoResponse,
    ErrorResponse,
    HealthResponse
)
from ..processor import QwenProcessor
from utils.monitor import SystemMonitor


# 全局处理器实例
_processor: Optional[QwenProcessor] = None


def get_processor() -> QwenProcessor:
    """获取处理器实例"""
    global _processor
    if _processor is None:
        try:
            logger.info("初始化 Qwen 处理器...")
            _processor = QwenProcessor()
            logger.info("Qwen 处理器初始化完成")
        except Exception as e:
            logger.error(f"Qwen 处理器初始化失败: {e}")
            raise HTTPException(status_code=500, detail=f"模型初始化失败: {str(e)}")
    return _processor


def get_monitor() -> SystemMonitor:
    """获取Monitor实例"""
    processor = get_processor()
    return processor.monitor


# 创建路由器
router = APIRouter()


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    try:
        processor = get_processor()
        model_loaded = processor.model is not None

        return HealthResponse(
            status="healthy",
            version="1.0.0",
            model_loaded=model_loaded,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate", response_model=GenerateTextResponse)
async def generate_text(request: GenerateTextRequest):
    """文本生成接口"""
    global _processor
    try:
        processor = get_processor()

        # 调用处理器生成文本
        result = processor.generate_text(
            prompt=request.prompt,
            system_prompt=request.system_prompt,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            top_p=request.top_p,
            top_k=request.top_k,
            min_p=request.min_p,
            repeat_penalty=request.repeat_penalty,
            presence_penalty=request.presence_penalty,
            stop=request.stop,
            task_type=request.task_type,
            enable_thinking=request.enable_thinking
        )

        # 显存管理操作
        memory_management_result = {}

        # 1. 清理GPU缓存
        if request.clear_cache_after:
            logger.info("执行生成后GPU缓存清理...")
            try:
                monitor = get_monitor()
                device_id = 0  # 默认使用第一个GPU
                cache_result = monitor.clear_gpu_memory(device_id)
                memory_management_result["cache_cleared"] = cache_result
                logger.info(f"GPU缓存清理完成，释放了 {cache_result.get('memory_freed_gb', 0)} GB")
            except Exception as e:
                logger.error(f"GPU缓存清理失败: {e}")
                memory_management_result["cache_cleared"] = {"success": False, "error": str(e)}

        # 2. 卸载模型
        if request.unload_model_after:
            logger.info("执行生成后模型卸载...")
            try:
                unload_result = processor.unload_model()
                memory_management_result["model_unloaded"] = unload_result

                # 如果模型被卸载，清除全局处理器实例
                if unload_result.get("success", False):
                    _processor = None
                    memory_management_result["processor_cleared"] = True
                    logger.info(f"模型卸载完成，释放了 {unload_result.get('memory_freed_gb', 0)} GB")
            except Exception as e:
                logger.error(f"模型卸载失败: {e}")
                memory_management_result["model_unloaded"] = {"success": False, "error": str(e)}

        # 添加显存管理结果到响应
        if memory_management_result:
            result["memory_management"] = memory_management_result

        return GenerateTextResponse(**result)

    except ValueError as e:
        logger.error(f"输入验证错误: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"文本生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"文本生成失败: {str(e)}")


@router.get("/model/info", response_model=ModelInfoResponse)
async def get_model_info():
    """获取模型信息"""
    try:
        processor = get_processor()
        model_info = processor.get_model_info()

        if "error" in model_info:
            raise HTTPException(status_code=500, detail=model_info["error"])

        return ModelInfoResponse(**model_info)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模型信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/model/reload")
async def reload_model():
    """重新加载模型"""
    global _processor
    try:
        logger.info("重新加载模型...")
        _processor = None  # 清除现有实例
        new_processor = get_processor()  # 重新初始化

        return {
            "message": "模型重新加载成功",
            "model_loaded": new_processor.model is not None,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        logger.error(f"模型重新加载失败: {e}")
        raise HTTPException(status_code=500, detail=f"模型重新加载失败: {str(e)}")


@router.get("/model/status")
async def get_model_status():
    """获取模型状态"""
    try:
        processor = get_processor()

        return {
            "loaded": processor.model is not None,
            "device_info": processor.device_info,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        logger.error(f"获取模型状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/model/clear-memory")
async def clear_gpu_memory():
    """清理GPU显存"""
    try:
        monitor = get_monitor()
        device_id = 0  # 默认使用第一个GPU
        result = monitor.clear_gpu_memory(device_id)

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("error", "显存清理失败"))

    except Exception as e:
        logger.error(f"显存清理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/model/unload")
async def unload_model():
    """卸载模型，完全释放显存"""
    global _processor
    try:
        if _processor is not None:
            result = _processor.unload_model()
            _processor = None  # 清除全局实例

            if result["success"]:
                return result
            else:
                raise HTTPException(status_code=500, detail=result.get("error", "模型卸载失败"))
        else:
            return {
                "success": True,
                "message": "模型未加载，无需卸载",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }

    except Exception as e:
        logger.error(f"模型卸载失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/model/memory-info")
async def get_memory_info():
    """获取详细的显存使用信息"""
    try:
        processor = get_processor()
        monitor = get_monitor()

        # 直接从Monitor获取GPU信息
        gpu_info = monitor.get_gpu_info()
        device_id = 0  # 默认使用第一个GPU

        memory_info = {}
        if gpu_info["available"] and device_id < len(gpu_info["devices"]):
            device = gpu_info["devices"][device_id]
            # 获取真实GPU内存使用情况
            real_memory_mb = monitor.get_real_gpu_memory(device_id)
            real_memory_gb = real_memory_mb / 1024

            memory_info = {
                "real_gpu_memory_used_gb": round(real_memory_gb, 2),
                "real_gpu_memory_used_mb": round(real_memory_mb, 2),
                "pytorch_memory_allocated_gb": device.get("memory_allocated", 0),
                "pytorch_memory_reserved_gb": device.get("memory_reserved", 0),
                "gpu_memory_total_gb": device.get("memory_total", 0),
                "gpu_memory_percent": device.get("memory_percent", 0)
            }

        return {
            "model_loaded": processor.model is not None,
            "memory_info": memory_info,
            "auto_clear_memory": processor.get_config_value("performance.auto_clear_memory", False),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        logger.error(f"获取显存信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system/monitor")
async def get_system_monitor_status():
    """获取系统监控状态"""
    try:
        monitor = get_monitor()

        # 直接从Monitor获取当前系统状态
        current_status = monitor.get_current_status()

        return {
            "system_status": current_status,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        logger.error(f"获取系统监控状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/system/clear-gpu-cache")
async def clear_gpu_cache(device_id: Optional[int] = None):
    """清理GPU缓存（不同于清理GPU内存）"""
    try:
        monitor = get_monitor()

        # 直接调用Monitor的GPU缓存清理方法
        monitor.clear_gpu_cache(device_id)

        return {
            "success": True,
            "message": f"GPU {'所有设备' if device_id is None else f'设备 {device_id}'} 缓存清理完成",
            "device_id": device_id,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        logger.error(f"GPU缓存清理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))