#!/bin/bash
"""
Qwen 模块自动环境切换启动脚本
自动切换到 audio_processor_env 环境并启动 Qwen API 服务器
"""

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 conda 是否可用
check_conda() {
    if ! command -v conda &> /dev/null; then
        print_error "conda 未找到，请确保已安装 Anaconda 或 Miniconda"
        exit 1
    fi
    print_success "conda 已找到"
}

# 检查环境是否存在
check_environment() {
    local env_name="audio_processor_env"
    
    if conda env list | grep -q "^${env_name} "; then
        print_success "环境 ${env_name} 已存在"
        return 0
    else
        print_error "环境 ${env_name} 不存在"
        print_info "请先创建环境："
        print_info "  conda create -n audio_processor_env python=3.11 -y"
        print_info "  conda activate audio_processor_env"
        print_info "  pip install llama-cpp-python torch fastapi uvicorn"
        exit 1
    fi
}

# 检查必要的包是否已安装
check_dependencies() {
    local env_name="audio_processor_env"
    
    print_info "检查环境 ${env_name} 中的依赖..."
    
    # 激活环境并检查包
    source "$(conda info --base)/etc/profile.d/conda.sh"
    conda activate "${env_name}"
    
    local missing_packages=()
    
    # 检查 llama-cpp-python
    if ! python -c "import llama_cpp" &> /dev/null; then
        missing_packages+=("llama-cpp-python")
    fi
    
    # 检查 torch
    if ! python -c "import torch" &> /dev/null; then
        missing_packages+=("torch")
    fi
    
    # 检查 fastapi
    if ! python -c "import fastapi" &> /dev/null; then
        missing_packages+=("fastapi")
    fi
    
    # 检查 uvicorn
    if ! python -c "import uvicorn" &> /dev/null; then
        missing_packages+=("uvicorn")
    fi
    
    if [ ${#missing_packages[@]} -gt 0 ]; then
        print_error "缺少以下包: ${missing_packages[*]}"
        print_info "请安装缺少的包："
        print_info "  conda activate audio_processor_env"
        for package in "${missing_packages[@]}"; do
            print_info "  pip install ${package}"
        done
        exit 1
    fi
    
    print_success "所有依赖已安装"
}

# 检查模型文件是否存在
check_model_file() {
    local model_file="Qwen3-32B-Q5_0.gguf"
    
    if [ -f "${model_file}" ]; then
        print_success "模型文件 ${model_file} 已找到"
    else
        print_warning "模型文件 ${model_file} 未找到"
        print_info "请确保模型文件在 qwen 目录中"
        print_info "如果没有模型文件，Qwen 服务器可能无法正常启动"
    fi
}

# 启动 Qwen 服务器
start_qwen_server() {
    local env_name="audio_processor_env"
    
    print_info "切换到环境 ${env_name}..."
    source "$(conda info --base)/etc/profile.d/conda.sh"
    conda activate "${env_name}"
    
    print_success "已切换到环境 ${env_name}"
    print_info "Python 版本: $(python --version)"
    
    # 设置 PYTHONPATH 以确保可以找到共享模块
    export PYTHONPATH="$(dirname "$(pwd)"):${PYTHONPATH}"
    
    print_info "启动 Qwen API 服务器..."
    print_info "PYTHONPATH: ${PYTHONPATH}"
    
    # 传递所有参数给 Python 脚本
    python -m qwen.cli "$@"
}

# 显示帮助信息
show_help() {
    echo "Qwen 模块自动环境切换启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --host HOST        服务器主机地址 (默认: 0.0.0.0)"
    echo "  --port PORT        服务器端口 (默认: 8000)"
    echo "  --reload           启用自动重载（开发模式）"
    echo "  --log-level LEVEL  日志级别 (默认: info)"
    echo "  -v, --verbose      详细输出"
    echo "  -h, --help         显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                           # 使用默认设置启动"
    echo "  $0 --port 9000              # 在端口 9000 启动"
    echo "  $0 --host 127.0.0.1 --reload # 本地开发模式"
    echo ""
    echo "环境要求:"
    echo "  - conda 环境: audio_processor_env"
    echo "  - Python 包: llama-cpp-python, torch, fastapi, uvicorn"
    echo "  - 模型文件: Qwen3-32B-Q5_0.gguf (可选)"
}

# 主函数
main() {
    # 检查是否请求帮助
    for arg in "$@"; do
        if [[ "$arg" == "-h" || "$arg" == "--help" ]]; then
            show_help
            exit 0
        fi
    done
    
    print_info "🚀 Qwen 模块自动环境切换启动脚本"
    print_info "=" * 50
    
    # 执行检查
    check_conda
    check_environment
    check_dependencies
    check_model_file
    
    print_info "=" * 50
    print_success "所有检查通过，准备启动 Qwen 服务器..."
    
    # 启动服务器
    start_qwen_server "$@"
}

# 如果脚本被直接执行，运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
