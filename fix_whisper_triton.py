#!/usr/bin/env python3
"""
修复 OpenAI Whisper 的 Triton 兼容性问题
"""

import sys
import os
from pathlib import Path

def fix_triton_import():
    """修复 triton_ops.py 中的 dtw_kernel 导入问题"""
    print("🔧 修复 Whisper Triton 兼容性问题...")
    
    try:
        import whisper
        whisper_path = Path(whisper.__file__).parent
        triton_ops_file = whisper_path / "triton_ops.py"
        
        if not triton_ops_file.exists():
            print(f"❌ 未找到 triton_ops.py 文件: {triton_ops_file}")
            return False
        
        print(f"📋 找到 triton_ops.py: {triton_ops_file}")
        
        # 读取原文件
        with open(triton_ops_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经修复
        if "# FIXED: dtw_kernel import" in content:
            print("✅ triton_ops.py 已经修复过了")
            return True
        
        # 创建备份
        backup_file = triton_ops_file.with_suffix('.py.backup')
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"📋 创建备份文件: {backup_file}")
        
        # 修复导入问题
        fixed_content = content
        
        # 方法1: 添加 try-except 包装
        if "from .triton_ops import dtw_kernel" in content:
            fixed_content = fixed_content.replace(
                "from .triton_ops import dtw_kernel",
                """# FIXED: dtw_kernel import
try:
    from .triton_ops import dtw_kernel
except ImportError:
    dtw_kernel = None"""
            )
        
        # 方法2: 修复 dtw_kernel 函数调用
        if "dtw_kernel" in content and "def " not in content:
            # 如果文件中有 dtw_kernel 的使用但没有定义，添加一个占位符
            if "def dtw_kernel" not in content:
                fixed_content = f"""# FIXED: dtw_kernel import
def dtw_kernel(*args, **kwargs):
    raise NotImplementedError("dtw_kernel is not available due to Triton compatibility issues")

{fixed_content}"""
        
        # 写入修复后的文件
        with open(triton_ops_file, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print("✅ triton_ops.py 修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def test_whisper_import():
    """测试修复后的 Whisper 导入"""
    print("\n🔍 测试修复后的 Whisper 导入...")
    
    try:
        import whisper
        print("✅ whisper 库导入成功")
        
        # 测试模型加载
        print("📋 测试模型加载...")
        model = whisper.load_model("tiny")  # 使用 tiny 模型进行快速测试
        print("✅ 模型加载成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def alternative_fix():
    """替代修复方案：禁用 Triton 优化"""
    print("\n🔧 尝试替代修复方案：禁用 Triton 优化...")
    
    try:
        # 设置环境变量禁用 Triton
        os.environ["WHISPER_NO_TRITON"] = "1"
        os.environ["TRITON_DISABLE"] = "1"
        
        import whisper
        print("✅ 使用禁用 Triton 的方式导入成功")
        
        # 测试模型加载
        model = whisper.load_model("tiny")
        print("✅ 模型加载成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 替代方案失败: {e}")
        return False

def main():
    """主修复函数"""
    print("🚀 修复 OpenAI Whisper Triton 兼容性问题")
    print("=" * 60)
    
    # 方案1: 修复 triton_ops.py 文件
    if fix_triton_import():
        if test_whisper_import():
            print("\n🎉 修复成功！")
            print("✅ Whisper 库现在可以正常使用")
            return True
    
    # 方案2: 使用环境变量禁用 Triton
    print("\n📋 尝试替代方案...")
    if alternative_fix():
        print("\n🎉 替代方案成功！")
        print("✅ Whisper 库现在可以正常使用（禁用 Triton 优化）")
        print("💡 建议在使用 Whisper 前设置环境变量:")
        print("   export WHISPER_NO_TRITON=1")
        print("   export TRITON_DISABLE=1")
        return True
    
    print("\n❌ 所有修复方案都失败了")
    print("💡 建议:")
    print("   1. 使用 camel_tools_env 环境")
    print("   2. 降级到 Python 3.11 或更早版本")
    print("   3. 使用 faster-whisper 库作为替代")
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
