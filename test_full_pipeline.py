#!/usr/bin/env python3
"""
完整音频转录流程测试
使用 data/samples/ 中的真实音频文件测试原始OpenAI Whisper转录引擎
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_audio_files():
    """检查可用的音频文件"""
    print("🔍 检查可用的音频文件...")
    
    samples_dir = Path("data/samples")
    if not samples_dir.exists():
        print("❌ data/samples 目录不存在")
        return None
    
    # 查找音频文件
    audio_extensions = ['.mp3', '.wav', '.flac', '.m4a', '.ogg']
    audio_files = []
    
    for ext in audio_extensions:
        audio_files.extend(list(samples_dir.glob(f"*{ext}")))
    
    if not audio_files:
        print("❌ 未找到音频文件")
        return None
    
    print(f"✅ 找到 {len(audio_files)} 个音频文件:")
    for i, file in enumerate(audio_files):
        print(f"   {i+1}. {file.name}")
    
    # 返回第一个音频文件
    selected_file = audio_files[0]
    print(f"📋 选择测试文件: {selected_file.name}")
    
    return selected_file

def test_basic_import():
    """测试基本导入"""
    print("\n🔍 测试基本导入...")
    
    try:
        # 测试whisper库导入
        import whisper
        print("✅ whisper库导入成功")
        
        # 测试项目转录器导入
        from audio_processor.transcribing import WhisperTranscriber
        print("✅ WhisperTranscriber导入成功")
        print(f"📋 转录器类型: {WhisperTranscriber.__name__}")
        print(f"📋 模块路径: {WhisperTranscriber.__module__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_transcriber_config():
    """测试转录器配置"""
    print("\n🔍 测试转录器配置...")
    
    try:
        from audio_processor.transcribing import WhisperTranscriber
        
        # 创建转录器实例（不加载模型）
        transcriber = WhisperTranscriber("whisper", enable_preprocessing=False)
        
        # 检查配置
        model_name = transcriber.get_config_value("model.name", "unknown")
        print(f"📋 配置的模型名称: {model_name}")
        
        # 检查转录参数
        transcription_config = transcriber.get_config_value("transcription", {})
        print(f"📋 转录配置参数数量: {len(transcription_config)}")
        
        # 测试参数构建
        params = transcriber._build_transcription_params(
            language="zh",  # 中文
            temperature=0.0,
            word_timestamps=True,
            verbose=True
        )
        
        print(f"📋 构建的参数数量: {len(params)}")
        print("📋 关键参数:")
        for key in ["language", "temperature", "word_timestamps", "verbose"]:
            if key in params:
                print(f"   ✅ {key}: {params[key]}")
            else:
                print(f"   ❌ 缺少: {key}")
        
        return transcriber
        
    except Exception as e:
        print(f"❌ 转录器配置测试失败: {e}")
        return None

def test_direct_whisper_transcription(audio_file):
    """测试直接使用原始Whisper转录"""
    print(f"\n🔍 测试直接Whisper转录: {audio_file.name}")
    
    try:
        import whisper
        
        # 加载模型（使用tiny模型进行快速测试）
        print("📋 加载tiny模型进行测试...")
        start_time = time.time()
        model = whisper.load_model("tiny")
        load_time = time.time() - start_time
        print(f"✅ 模型加载完成 (耗时: {load_time:.2f}秒)")
        
        # 执行转录
        print("📋 开始转录...")
        start_time = time.time()
        result = model.transcribe(
            str(audio_file),
            language="zh",  # 中文
            task="transcribe",
            verbose=True,
            word_timestamps=True,
            condition_on_previous_text=True,
            temperature=0.0
        )
        transcribe_time = time.time() - start_time
        print(f"✅ 转录完成 (耗时: {transcribe_time:.2f}秒)")
        
        # 分析结果
        print("\n📋 转录结果分析:")
        print(f"   - 完整文本: {result.get('text', 'N/A')[:100]}...")
        print(f"   - 检测语言: {result.get('language', 'N/A')}")
        print(f"   - 段落数量: {len(result.get('segments', []))}")
        
        # 检查词级时间戳
        segments = result.get('segments', [])
        if segments:
            first_segment = segments[0]
            print(f"   - 第一段: [{first_segment.get('start', 'N/A'):.2f} --> {first_segment.get('end', 'N/A'):.2f}] {first_segment.get('text', 'N/A')}")
            
            if 'words' in first_segment:
                words = first_segment['words']
                print(f"   ✅ 词级时间戳: {len(words)}个词")
                # 显示前3个词
                for i, word in enumerate(words[:3]):
                    print(f"      {i+1}. {word.get('word', 'N/A')}: [{word.get('start', 'N/A'):.2f} --> {word.get('end', 'N/A'):.2f}]")
            else:
                print("   ⚠️  未找到词级时间戳")
        
        return result
        
    except Exception as e:
        print(f"❌ 直接Whisper转录失败: {e}")
        return None

def test_project_transcriber_transcription(audio_file):
    """测试项目转录器转录"""
    print(f"\n🔍 测试项目转录器转录: {audio_file.name}")
    
    try:
        from audio_processor.transcribing import WhisperTranscriber
        
        # 创建转录器（不启用预处理，先测试基本功能）
        print("📋 创建转录器（无预处理）...")
        transcriber = WhisperTranscriber("whisper", enable_preprocessing=False)
        
        # 执行转录
        print("📋 开始转录...")
        start_time = time.time()
        result = transcriber.transcribe(
            str(audio_file),
            language="zh",  # 中文
            task="transcribe",
            verbose=True,
            word_timestamps=True,
            condition_on_previous_text=True,
            temperature=0.0
        )
        transcribe_time = time.time() - start_time
        print(f"✅ 项目转录器转录完成 (耗时: {transcribe_time:.2f}秒)")
        
        # 分析结果
        print("\n📋 项目转录器结果分析:")
        print(f"   - 完整文本: {result.get('text', 'N/A')[:100]}...")
        print(f"   - 检测语言: {result.get('language', 'N/A')}")
        print(f"   - 段落数量: {len(result.get('segments', []))}")
        
        # 检查词级时间戳
        segments = result.get('segments', [])
        if segments:
            first_segment = segments[0]
            print(f"   - 第一段: [{first_segment.get('start', 'N/A'):.2f} --> {first_segment.get('end', 'N/A'):.2f}] {first_segment.get('text', 'N/A')}")
            
            if 'words' in first_segment:
                words = first_segment['words']
                print(f"   ✅ 词级时间戳: {len(words)}个词")
                # 显示前3个词
                for i, word in enumerate(words[:3]):
                    print(f"      {i+1}. {word.get('word', 'N/A')}: [{word.get('start', 'N/A'):.2f} --> {word.get('end', 'N/A'):.2f}]")
            else:
                print("   ⚠️  未找到词级时间戳")
        
        return result
        
    except Exception as e:
        print(f"❌ 项目转录器转录失败: {e}")
        return None

def test_with_preprocessing(audio_file):
    """测试带预处理的转录"""
    print(f"\n🔍 测试带预处理的转录: {audio_file.name}")
    
    try:
        from audio_processor.transcribing import WhisperTranscriber
        
        # 创建转录器（启用预处理）
        print("📋 创建转录器（启用预处理）...")
        transcriber = WhisperTranscriber("whisper", enable_preprocessing=True)
        
        # 执行转录
        print("📋 开始预处理+转录...")
        start_time = time.time()
        result = transcriber.transcribe(
            str(audio_file),
            language="zh",  # 中文
            task="transcribe",
            verbose=True,
            word_timestamps=True,
            condition_on_previous_text=True,
            temperature=0.0
        )
        total_time = time.time() - start_time
        print(f"✅ 预处理+转录完成 (总耗时: {total_time:.2f}秒)")
        
        # 分析结果
        print("\n📋 预处理转录结果分析:")
        print(f"   - 完整文本: {result.get('text', 'N/A')[:100]}...")
        print(f"   - 检测语言: {result.get('language', 'N/A')}")
        print(f"   - 段落数量: {len(result.get('segments', []))}")
        
        return result
        
    except Exception as e:
        print(f"❌ 预处理转录失败: {e}")
        return None

def main():
    """主测试函数"""
    print("🚀 完整音频转录流程测试")
    print("使用 data/samples/ 中的真实音频文件")
    print("=" * 60)
    
    # 检查音频文件
    audio_file = check_audio_files()
    if not audio_file:
        print("❌ 无法找到测试音频文件")
        return False
    
    # 测试步骤
    tests_passed = []
    
    # 1. 基本导入测试
    if test_basic_import():
        tests_passed.append("基本导入")
    
    # 2. 转录器配置测试
    transcriber = test_transcriber_config()
    if transcriber:
        tests_passed.append("转录器配置")
    
    # 3. 直接Whisper转录测试
    direct_result = test_direct_whisper_transcription(audio_file)
    if direct_result:
        tests_passed.append("直接Whisper转录")
    
    # 4. 项目转录器测试
    project_result = test_project_transcriber_transcription(audio_file)
    if project_result:
        tests_passed.append("项目转录器转录")
    
    # 5. 预处理转录测试
    preprocessing_result = test_with_preprocessing(audio_file)
    if preprocessing_result:
        tests_passed.append("预处理转录")
    
    # 总结
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {len(tests_passed)}/5 项测试通过")
    print("📋 通过的测试:")
    for test in tests_passed:
        print(f"   ✅ {test}")
    
    if len(tests_passed) >= 3:
        print("\n🎉 全流程测试成功！")
        print("✅ 原始OpenAI Whisper转录引擎工作正常")
        print("✅ 词级别时间戳功能正常")
        print("✅ 项目转录器集成成功")
        if len(tests_passed) >= 5:
            print("✅ 预处理功能集成正常")
        
        print(f"\n📋 测试音频: {audio_file.name}")
        print("📋 支持的功能:")
        print("   - 原始OpenAI Whisper API")
        print("   - 词级别时间戳")
        print("   - 中文语音识别")
        print("   - 项目转录器封装")
        if len(tests_passed) >= 5:
            print("   - 音频预处理流程")
        
        return True
    else:
        print("\n⚠️  部分测试失败")
        print("💡 建议检查:")
        print("   - conda环境是否正确激活")
        print("   - openai-whisper库是否正确安装")
        print("   - 音频文件是否可访问")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
