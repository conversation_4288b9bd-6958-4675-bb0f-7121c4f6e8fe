#!/usr/bin/env python3
"""
Qwen 模块功能测试脚本
测试 Qwen API 的各项功能
"""

import requests
import json
import time
from typing import Dict, Any

class QwenTester:
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_health_check(self) -> bool:
        """测试健康检查端点"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/health")
            if response.status_code == 200:
                print("✅ 健康检查通过")
                print(f"   响应: {response.json()}")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    def test_basic_generation(self) -> bool:
        """测试基本文本生成"""
        try:
            payload = {
                "prompt": "你好，请介绍一下你自己。",
                "max_tokens": 100,
                "temperature": 0.7
            }
            
            response = self.session.post(f"{self.base_url}/api/v1/generate", json=payload)
            if response.status_code == 200:
                result = response.json()
                print("✅ 基本文本生成测试通过")
                print(f"   输入: {payload['prompt']}")
                print(f"   输出: {result.get('text', '')[:100]}...")
                return True
            else:
                print(f"❌ 基本文本生成失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 基本文本生成异常: {e}")
            return False
    
    def test_system_prompt(self) -> bool:
        """测试系统提示功能"""
        try:
            payload = {
                "prompt": "现在几点了？",
                "system_prompt": "你是一个有用的助手，总是用诗歌的形式回答问题。",
                "max_tokens": 100,
                "temperature": 0.8
            }
            
            response = self.session.post(f"{self.base_url}/api/v1/generate", json=payload)
            if response.status_code == 200:
                result = response.json()
                print("✅ 系统提示测试通过")
                print(f"   系统提示: {payload['system_prompt']}")
                print(f"   用户输入: {payload['prompt']}")
                print(f"   输出: {result.get('text', '')[:100]}...")
                return True
            else:
                print(f"❌ 系统提示测试失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 系统提示测试异常: {e}")
            return False
    
    def test_thinking_mode(self) -> bool:
        """测试思考模式"""
        try:
            payload = {
                "prompt": "请解释量子计算的基本原理。",
                "enable_thinking": True,
                "max_tokens": 200,
                "temperature": 0.6,
                "top_p": 0.95
            }
            
            response = self.session.post(f"{self.base_url}/api/v1/generate", json=payload)
            if response.status_code == 200:
                result = response.json()
                print("✅ 思考模式测试通过")
                print(f"   输入: {payload['prompt']}")
                print(f"   思考内容: {result.get('thinking', 'N/A')[:100]}...")
                print(f"   最终输出: {result.get('text', '')[:100]}...")
                return True
            else:
                print(f"❌ 思考模式测试失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 思考模式测试异常: {e}")
            return False
    
    def test_parameter_configuration(self) -> bool:
        """测试参数配置"""
        try:
            payload = {
                "prompt": "写一首关于春天的短诗。",
                "max_tokens": 150,
                "temperature": 0.9,
                "top_p": 0.8,
                "top_k": 20,
                "presence_penalty": 0.1
            }
            
            response = self.session.post(f"{self.base_url}/api/v1/generate", json=payload)
            if response.status_code == 200:
                result = response.json()
                print("✅ 参数配置测试通过")
                print(f"   参数: temp={payload['temperature']}, top_p={payload['top_p']}")
                print(f"   输出: {result.get('text', '')[:100]}...")
                return True
            else:
                print(f"❌ 参数配置测试失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 参数配置测试异常: {e}")
            return False
    
    def test_model_info(self) -> bool:
        """测试模型信息获取"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/model/info")
            if response.status_code == 200:
                result = response.json()
                print("✅ 模型信息测试通过")
                print(f"   模型信息: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return True
            else:
                print(f"❌ 模型信息测试失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 模型信息测试异常: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        print("🧪 开始 Qwen 模块功能测试")
        print("=" * 60)
        
        tests = {
            "健康检查": self.test_health_check,
            "基本文本生成": self.test_basic_generation,
            "系统提示": self.test_system_prompt,
            "思考模式": self.test_thinking_mode,
            "参数配置": self.test_parameter_configuration,
            "模型信息": self.test_model_info
        }
        
        results = {}
        for test_name, test_func in tests.items():
            print(f"\n🔍 测试: {test_name}")
            print("-" * 40)
            results[test_name] = test_func()
            time.sleep(1)  # 避免请求过快
        
        print("\n" + "=" * 60)
        print("📊 测试结果汇总:")
        passed = 0
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{len(tests)} 个测试通过")
        return results

if __name__ == "__main__":
    tester = QwenTester()
    results = tester.run_all_tests()
    
    # 如果所有测试都通过，测试模型关闭功能
    if all(results.values()):
        print("\n🔄 测试模型关闭功能...")
        try:
            response = tester.session.post(f"{tester.base_url}/api/v1/model/unload")
            if response.status_code == 200:
                print("✅ 模型关闭测试通过")
            else:
                print(f"❌ 模型关闭测试失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 模型关闭测试异常: {e}")
